# 代理商权限控制测试指南

## 实现的功能

### 1. 菜单权限控制
- **管理员 (ADMIN)**: 可以看到所有菜单
- **代理商 (AGENT)**: 只能看到"控制台"和"授权令牌管理"两个菜单
- **普通用户 (USER)**: 只能看到"控制台"菜单

### 2. 数据隔离
- **管理员**: 可以查看和操作所有授权令牌数据
- **代理商**: 只能查看和操作自己创建的授权令牌数据
- **普通用户**: 暂时没有访问权限

### 3. 页面访问控制
- **用户管理页面**: 只有管理员可以访问，代理商访问会返回403错误

## 测试步骤

### 1. 创建测试用户
在数据库中创建不同角色的用户进行测试：

```sql
-- 创建管理员用户
INSERT INTO "User" (id, username, password, role, status) 
VALUES ('admin-test', 'admin', '$2a$10$hashedpassword', 'ADMIN', 'ACTIVE');

-- 创建代理商用户
INSERT INTO "User" (id, username, password, role, status) 
VALUES ('agent-test', 'agent', '$2a$10$hashedpassword', 'AGENT', 'ACTIVE');
```

### 2. 测试菜单显示
1. 使用管理员账号登录，应该看到所有菜单
2. 使用代理商账号登录，应该只看到"控制台"和"授权令牌管理"菜单

### 3. 测试数据隔离
1. 使用管理员账号创建一些授权令牌
2. 使用代理商账号创建一些授权令牌
3. 切换到代理商账号，应该只能看到自己创建的授权令牌

### 4. 测试页面访问控制
1. 使用代理商账号尝试访问 `/users` 页面
2. 应该返回403 Forbidden错误

## 数据库变更

### 新增字段
在 `InviteCode` 表中添加了 `createdBy` 字段：
- 类型: String (可选)
- 用途: 记录创建授权令牌的用户ID
- 关联: 与 User 表的 id 字段关联

### 数据迁移
已执行 `npx prisma db push` 来同步数据库结构。

## 代码变更

### 1. 权限工具函数 (`app/utils/permissions.server.ts`)
- `requireAdmin`: 检查管理员权限
- `requireAgentOrAdmin`: 检查代理商或管理员权限
- `getAccessibleMenus`: 根据角色获取可访问菜单
- `canAccessPath`: 检查路径访问权限
- `addAgentDataFilter`: 为代理商添加数据过滤条件

### 2. Layout组件更新
- `Layout.tsx`: 根据用户角色过滤显示的菜单
- `SidebarLayout.tsx`: 同样支持角色过滤

### 3. 授权令牌服务更新
- `inviteCodeService.ts`: 
  - 在创建授权令牌时记录创建者
  - 在查询时根据用户角色过滤数据

### 4. 用户管理页面
- `users.tsx`: 添加管理员权限检查

## 注意事项

1. **现有数据**: 现有的授权令牌没有 `createdBy` 字段，管理员可以看到所有数据，代理商只能看到自己创建的新数据
2. **权限检查**: 所有需要权限控制的页面都应该添加相应的权限检查
3. **API接口**: 需要确保所有API接口都有适当的权限验证
4. **前端显示**: 前端组件需要根据用户角色显示不同的功能按钮

## 后续改进

1. **完善权限系统**: 可以考虑实现更细粒度的权限控制
2. **审计日志**: 记录用户操作日志
3. **角色管理**: 添加角色管理功能
4. **数据迁移**: 为现有数据添加创建者信息
