/**
 * 授权令牌服务 - 处理数据加载和操作
 */
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId, getUser } from "~/utils/session.server";
import { generateInviteCode } from "~/utils/inviteCodeUtils";
import crypto from "crypto";

/**
 * 加载授权令牌数据
 */
export async function loadInviteCodes({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  const url = new URL(request.url);
  const searchParams = new URLSearchParams(url.search);
  const page = parseInt(searchParams.get("page") || "1", 10);
  const pageSize = parseInt(searchParams.get("pageSize") || "10", 10);
  const tab = searchParams.get("tab") || "active";
  const skip = (page - 1) * pageSize;
  const search = searchParams.get("search") || "";
  const searchType = searchParams.get("searchType") || "wechatId";

  // 基础查询条件
  let where: any = {
    deletedAt: tab === "active" ? null : { not: null },
    ...(search ? {
      [searchType]: {
        contains: search,
        mode: 'insensitive',
      }
    } : {}),
  };

  // 代理商只能看到自己创建的数据
  if (user?.role === 'AGENT') {
    where.createdBy = user.id;
  }

  const inviteCodes = await db.inviteCode.findMany({
    where,
    orderBy: { createdAt: "desc" },
    skip,
    take: pageSize,
    include: {
      deviceSessions: {
        select: {
          deviceId: true,
          lastActiveAt: true,
          id: true,
          metadata: true
        }
      },
      product: {
        select: {
          id: true,
          name: true,
          version: true,
          isEnabled: true
        }
      },
      paymentRecords: {
        orderBy: {
          paidAt: 'desc'
        },
        select: {
          id: true,
          amount: true,
          paidAt: true,
          extensionDays: true,
          remark: true
        }
      },
      tikTokAccounts: {
        select: {
          id: true,
          nickName: true,
          enabled: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
          cookiesList: false,
          deviceId: true // 确保包含deviceId字段
        }
      }
    }
  });

  // 对于每个授权码，确保其账号和设备关系正确
  for (const inviteCode of inviteCodes) {
    // 如果授权码允许多设备，我们需要确保每个账号都与正确的设备关联
    if (inviteCode.allowMultipleDevices) {
      // 对于每个没有deviceId的账号，尝试通过设备元数据关联
      for (const account of inviteCode.tikTokAccounts) {
        if (!account.deviceId) {
          // 查找在设备元数据中引用此账号的设备
          const matchingDevice = inviteCode.deviceSessions.find(
            session => session.metadata &&
                      typeof session.metadata === 'object' &&
                      session.metadata.tikTokAccountId === account.id
          );

          if (matchingDevice) {
            // 在内存中更新账号的deviceId
            account.deviceId = matchingDevice.deviceId;

            // 记录此关联以便调试
            console.log(`服务器端自动关联: 账号 ${account.nickName} -> 设备 ${matchingDevice.deviceId}`);

            // 实际更新数据库(这不会影响当前请求的结果，但会修复数据库中的关系)
            try {
              await db.tikTokAccount.update({
                where: { id: account.id },
                data: { deviceId: matchingDevice.deviceId }
              });
            } catch (err) {
              console.error(`更新账号 ${account.nickName} 的设备ID失败:`, err);
            }
          }
        }
      }
    } else {
      // 对于不允许多设备的授权码，所有账号应该关联到同一个设备
      if (inviteCode.deviceSessions.length > 0 && inviteCode.tikTokAccounts.length > 0) {
        // 使用最近活跃的设备
        const latestDevice = [...inviteCode.deviceSessions].sort(
          (a, b) => new Date(b.lastActiveAt).getTime() - new Date(a.lastActiveAt).getTime()
        )[0];

        // 更新所有没有deviceId的账号
        for (const account of inviteCode.tikTokAccounts) {
          if (!account.deviceId) {
            // 在内存中更新账号的deviceId
            account.deviceId = latestDevice.deviceId;

            // 记录此关联以便调试
            console.log(`服务器端自动关联(单设备模式): 账号 ${account.nickName} -> 设备 ${latestDevice.deviceId}`);

            // 实际更新数据库
            try {
              await db.tikTokAccount.update({
                where: { id: account.id },
                data: { deviceId: latestDevice.deviceId }
              });
            } catch (err) {
              console.error(`更新账号 ${account.nickName} 的设备ID失败:`, err);
            }
          }
        }
      }
    }
  }

  // 计算总收入（代理商只看自己创建的授权令牌的收入）
  let totalIncome;
  if (user?.role === 'AGENT') {
    const totalIncomeResult = await db.$queryRaw`
      SELECT COALESCE(SUM(pr.amount), 0) as total
      FROM "PaymentRecord" pr
      JOIN "InviteCode" ic ON pr."inviteCodeId" = ic.id
      WHERE ic."createdBy" = ${user.id}
    ` as any[];
    totalIncome = { _sum: { amount: Number(totalIncomeResult[0]?.total || 0) } };
  } else {
    totalIncome = await db.paymentRecord.aggregate({
      _sum: {
        amount: true
      }
    });
  }

  // 计算每个授权码的总付款金额
  const inivteCodesWithPaymentSum = inviteCodes.map(code => ({
    ...code,
    _sum: {
      paymentRecords: {
        amount: code.paymentRecords.reduce((sum, record) => sum + Number(record.amount), 0)
      }
    }
  }));

  const total = await db.inviteCode.count({ where });

  const products = await db.product.findMany({
    where: { isEnabled: true },
    select: { id: true, name: true, version: true }
  });

  // 确保分页数据都是有效的数字
  const totalPages = Math.ceil(total / pageSize) || 1; // 防止除以零或结果为零

  return json({
    inviteCodes: inivteCodesWithPaymentSum,
    user,
    pagination: {
      total,
      page,
      pageSize,
      totalPages
    },
    products,
    totalIncome: Number(totalIncome._sum.amount || 0)
  });
}

/**
 * 处理授权令牌相关操作
 */
export async function handleInviteCodeAction({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  console.log("处理授权令牌操作:", { intent });

  // 对于特定操作，不需要用户登录
  if (intent !== "updatePaymentRemark" && intent !== "addPayment" && intent !== "deletePayment") {
    await requireUserId(request);
  }

  // 获取用户信息
  const user = await getUser(request);

  try {
    switch (intent) {
      case "create": {
        return await createInviteCode(formData, user);
      }
      case "toggle": {
        return await toggleInviteCode(formData);
      }
      case "toggle-paid": {
        return await togglePaidStatus(formData);
      }
      case "delete": {
        return await deleteInviteCode(formData);
      }
      case "edit": {
        return await editInviteCode(formData);
      }
      case "renew": {
        return await renewInviteCode(formData);
      }
      case "updateMenuAuth": {
        return await updateMenuAuth(formData);
      }
      case "batchRenew": {
        return await batchRenewInviteCodes(formData);
      }
      case "clearDevice": {
        return await clearDevice(formData);
      }
      case "restore": {
        return await restoreInviteCode(formData);
      }
      case "permanentDelete": {
        return await permanentDeleteInviteCode(formData);
      }
      case "addPayment": {
        return await addPayment(formData);
      }
      case "updatePaymentRemark": {
        return await updatePaymentRemark(formData);
      }
      case "exportBill": {
        return await exportBill(formData);
      }
      case "deletePayment": {
        return await deletePayment(formData);
      }
      default:
        return json({ error: "无效的操作" }, { status: 400 });
    }
  } catch (error) {
    console.error("Action error:", error);
    return json({
      error: "操作失败，请重试" + (error instanceof Error ? `: ${error.message}` : "")
    }, { status: 500 });
  }
}

/**
 * 创建新的授权令牌
 */
async function createInviteCode(formData: FormData, user: any) {
  const maxAccountCount = Number(formData.get("maxAccountCount"));
  const wechatId = formData.get("wechatId") as string;
  const remark = formData.get("remark") as string;
  const expiresAtStr = formData.get("expiresAt") as string;
  const productId = formData.get("productId") as string;
  const allowMultipleDevices = formData.get("allowMultipleDevices") === "on";
  // 新增金额字段
  const amount = Number(formData.get("amount") || 0);
  // 新增套餐类型
  const cardType = formData.get("cardType") as string || "custom";

  // 根据套餐类型计算过期时间
  let expiresAt = null;
  let extensionDays = 0;

  if (cardType === "custom" && expiresAtStr) {
    // 自定义时间
    // 直接解析完整的日期时间字符串，保留时间部分
    expiresAt = new Date(expiresAtStr);

    // 如果日期无效，则使用当前时间加一个月
    if (isNaN(expiresAt.getTime())) {
      const now = new Date();
      expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }

    // 计算当前日期到过期日期的天数差
    const currentDate = new Date();
    const timeDiff = expiresAt.getTime() - currentDate.getTime();
    extensionDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
  } else {
    // 根据套餐类型设置过期时间
    const now = new Date();

    if (cardType === "day") {
      // 天卡 - 到当日的这个时间点
      expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      extensionDays = 1;
    } else if (cardType === "month") {
      // 月卡 - 30天
      expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      extensionDays = 30;
    } else if (cardType === "year") {
      // 年卡 - 366天
      expiresAt = new Date(now.getTime() + 366 * 24 * 60 * 60 * 1000);
      extensionDays = 366;
    }
  }

  if (!productId) {
    return json({ error: "请选择产品" }, { status: 400 });
  }

  // 创建授权令牌
  const inviteCode = await db.inviteCode.create({
    data: {
      code: generateInviteCode(),
      maxAccountCount,
      wechatId: wechatId || null,
      remark: remark || null,
      expiresAt,
      isEnabled: true,
      isPaid: amount > 0, // 如果金额大于0，则标记为已付费
      product: {
        connect: { id: productId }
      },
      allowMultipleDevices,
      enforceAccountLimit: true, // 默认启用账号数量限制
    },
  });

  // 单独更新 createdBy 字段（使用原生 SQL）
  if (user?.id) {
    await db.$executeRaw`
      UPDATE "InviteCode"
      SET "createdBy" = ${user.id}
      WHERE id = ${inviteCode.id}
    `;
  }

  // 如果有金额，创建付款记录
  if (amount > 0) {
    await db.paymentRecord.create({
      data: {
        inviteCodeId: inviteCode.id,
        amount,
        extensionDays,
        remark: cardType !== "custom"
          ? `创建${cardType === "day" ? "天卡" : cardType === "month" ? "月卡" : "年卡"}授权令牌的付款`
          : "创建授权时的初始付款",
      },
    });
  }

  return json({ success: true });
}

/**
 * 切换授权令牌启用状态
 */
async function toggleInviteCode(formData: FormData) {
  const id = formData.get("id") as string;
  const inviteCode = await db.inviteCode.findUnique({ where: { id } });
  if (!inviteCode) {
    return json({ error: "授权令牌不存在" }, { status: 404 });
  }
  await db.inviteCode.update({
    where: { id },
    data: { isEnabled: !inviteCode.isEnabled },
  });
  return json({ success: true });
}

/**
 * 切换授权令牌付费状态
 */
async function togglePaidStatus(formData: FormData) {
  const id = formData.get("id") as string;
  const inviteCode = await db.inviteCode.findUnique({ where: { id } });
  if (!inviteCode) {
    return json({ error: "授权令牌不存在" }, { status: 404 });
  }
  await db.inviteCode.update({
    where: { id },
    data: { isPaid: !inviteCode.isPaid },
  });
  return json({ success: true });
}

/**
 * 删除授权令牌（软删除）
 */
async function deleteInviteCode(formData: FormData) {
  const id = formData.get("id") as string;
  await db.inviteCode.update({
    where: { id },
    data: {
      deletedAt: new Date(),
      isEnabled: false,
      deviceSessions: {
        deleteMany: {}
      }
    }
  });
  return json({ success: true });
}

/**
 * 编辑授权令牌
 */
async function editInviteCode(formData: FormData) {
  const id = formData.get("id") as string;
  const wechatId = formData.get("wechatId") as string;
  const remark = formData.get("remark") as string;
  const maxAccountCount = Number(formData.get("maxAccountCount"));
  const allowMultipleDevices = formData.get("allowMultipleDevices") === "on";
  // enforceAccountLimit字段暂时不使用，等待数据库迁移完成

  // 修复时区问题
  let expiresAt = null;
  if (formData.get("expiresAt")) {
    const expiresAtStr = formData.get("expiresAt") as string;

    // 处理空字符串的情况
    if (expiresAtStr && expiresAtStr !== 'null' && expiresAtStr !== 'undefined') {
      // 解析日期字符串为Date对象
      const date = new Date(expiresAtStr);

      // 检查日期是否有效
      if (!isNaN(date.getTime())) {
        // 保留完整的时间信息，包括小时和分钟
        expiresAt = date;
      }
    }
  }

  const productId = formData.get("productId") as string;

  if (!productId) {
    return json({ error: "请选择产品" }, { status: 400 });
  }

  await db.inviteCode.update({
    where: { id },
    data: {
      wechatId: wechatId || null,
      remark: remark || null,
      maxAccountCount,
      expiresAt,
      product: {
        connect: { id: productId }
      },
      allowMultipleDevices
      // enforceAccountLimit字段暂时不添加，等待数据库迁移完成
    },
  });
  return json({ success: true });
}

/**
 * 续期授权令牌
 */
async function renewInviteCode(formData: FormData) {
  const id = formData.get("id") as string;

  // 解析日期时间，保留完整时间信息
  const newExpiryStr = formData.get("newExpiry") as string;
  let newExpiry;

  if (newExpiryStr) {
    // 直接解析完整的日期时间字符串，保留时间部分
    newExpiry = new Date(newExpiryStr);

    // 如果日期无效，则使用当前时间加一个月
    if (isNaN(newExpiry.getTime())) {
      newExpiry = new Date();
      newExpiry.setMonth(newExpiry.getMonth() + 1);
    }
  } else {
    // 默认一个月后
    newExpiry = new Date();
    newExpiry.setMonth(newExpiry.getMonth() + 1);
  }

  const newCount = Number(formData.get("newCount"));
  const fee = Number(formData.get("fee"));
  const remark = formData.get("remark") as string;
  const productId = formData.get("productId") as string;

  const inviteCode = await db.inviteCode.findUnique({ where: { id } });
  if (!inviteCode) {
    return json({ error: "授权令牌不存在" }, { status: 404 });
  }

  await db.inviteCode.update({
    where: { id },
    data: {
      expiresAt: newExpiry,
      maxAccountCount: newCount,
      product: {
        connect: { id: productId }
      }
    },
  });

  await db.renewalRecord.create({
    data: {
      inviteCodeId: id,
      previousExpiry: inviteCode.expiresAt,
      newExpiry,
      previousCount: inviteCode.maxAccountCount,
      newCount,
      fee,
      remark: remark || null,
    },
  });
  return json({ success: true });
}

/**
 * 更新菜单授权
 */
async function updateMenuAuth(formData: FormData) {
  const inviteCodeId = formData.get("inviteCodeId") as string;
  const menuIds = formData.getAll("menuIds[]") as string[];

  if (!inviteCodeId) {
    return json({ error: "授权令牌ID不能为空" }, { status: 400 });
  }

  try {
    // 获取当前授权令牌
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: inviteCodeId }
    });

    if (!inviteCode) {
      return json({ error: "授权令牌不存在" }, { status: 404 });
    }

    // 1. 删除该授权令牌的所有现有菜单关联
    await db.$executeRaw`DELETE FROM "InviteCodeMenu" WHERE "inviteCodeId" = ${inviteCodeId}`;

    // 2. 创建新的菜单关联
    for (const menuId of menuIds) {
      await db.$executeRaw`
        INSERT INTO "InviteCodeMenu" ("id", "inviteCodeId", "menuId", "createdAt", "updatedAt")
        VALUES (${crypto.randomUUID()}, ${inviteCodeId}, ${menuId}, NOW(), NOW())
      `;
    }

    return json({
      success: true,
      message: "菜单授权更新成功",
      data: {
        inviteCodeId,
        authorizedMenuIds: menuIds
      }
    });
  } catch (error) {
    console.error("更新菜单授权失败:", error);
    return json({
      error: "更新菜单授权失败，请重试"
    }, { status: 500 });
  }
}

/**
 * 批量续期授权令牌
 */
async function batchRenewInviteCodes(formData: FormData) {
  const ids = formData.getAll("ids[]") as string[];

  if (!ids.length) {
    return json({ error: "请选择要续期的授权令牌" }, { status: 400 });
  }

  // 解析日期时间，保留完整时间信息
  const newExpiryStr = formData.get("newExpiry") as string;
  let newExpiry;

  if (newExpiryStr) {
    // 直接解析完整的日期时间字符串，保留时间部分
    newExpiry = new Date(newExpiryStr);

    // 如果日期无效，则使用当前时间加一个月
    if (isNaN(newExpiry.getTime())) {
      newExpiry = new Date();
      newExpiry.setMonth(newExpiry.getMonth() + 1);
    }
  } else {
    // 默认一个月后
    newExpiry = new Date();
    newExpiry.setMonth(newExpiry.getMonth() + 1);
  }

  const newCount = formData.get("newCount")
    ? Number(formData.get("newCount"))
    : undefined;

  const fee = Number(formData.get("fee") || 0);
  const remark = formData.get("remark") as string;
  const productId = formData.get("productId") as string;
  const renewalMode = formData.get("renewalMode") as string;

  // 使用 Promise.all 处理批量更新
  await Promise.all(ids.map(async (id) => {
    const inviteCode = await db.inviteCode.findUnique({
      where: { id },
      select: {
        id: true,
        expiresAt: true,
        maxAccountCount: true,
      }
    });

    if (!inviteCode) return;

    // 处理不同的续期模式
    let finalExpiry = newExpiry;
    if (renewalMode === "extend" && inviteCode.expiresAt) {
      // 在原有过期时间基础上延长
      const extensionDays = Math.floor((newExpiry.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
      finalExpiry = new Date(inviteCode.expiresAt.getTime() + extensionDays * 24 * 60 * 60 * 1000);
    }

    // 更新授权令牌
    const updateData: {
      expiresAt: Date;
      maxAccountCount?: number;
      product?: {
        connect: { id: string }
      };
    } = {
      expiresAt: finalExpiry,
      product: productId ? {
        connect: { id: productId }
      } : undefined
    };

    // 只有当指定了新账号数量时才更新
    if (newCount !== undefined) {
      updateData.maxAccountCount = newCount;
    }

    await db.inviteCode.update({
      where: { id },
      data: updateData,
    });

    // 创建续期记录
    await db.renewalRecord.create({
      data: {
        inviteCodeId: id,
        previousExpiry: inviteCode.expiresAt,
        newExpiry: finalExpiry,
        previousCount: inviteCode.maxAccountCount,
        newCount: newCount !== undefined ? newCount : inviteCode.maxAccountCount,
        fee: fee / ids.length, // 平均分配费用
        remark: `批量续期: ${remark || "无备注"}`,
      },
    });
  }));

  return json({ success: true, message: `已成功续期 ${ids.length} 个授权令牌` });
}

/**
 * 清除设备
 */
async function clearDevice(formData: FormData) {
  const id = formData.get("id") as string;
  await db.deviceSession.deleteMany({
    where: { inviteCodeId: id },
  });
  return json({ success: true });
}

/**
 * 恢复已删除的授权令牌
 */
async function restoreInviteCode(formData: FormData) {
  const id = formData.get("id") as string;
  await db.inviteCode.update({
    where: { id },
    data: {
      deletedAt: null
    }
  });
  return json({ success: true });
}

/**
 * 永久删除授权令牌
 */
async function permanentDeleteInviteCode(formData: FormData) {
  const id = formData.get("id") as string;
  await db.$transaction([
    db.deviceSession.deleteMany({
      where: { inviteCodeId: id }
    }),
    db.inviteCode.delete({
      where: { id }
    })
  ]);
  return json({ success: true });
}

/**
 * 添加付款记录
 */
async function addPayment(formData: FormData) {
  const inviteCodeId = formData.get("inviteCodeId") as string;
  const amount = Number(formData.get("amount"));
  const extensionDays = Number(formData.get("extensionDays"));
  const remark = formData.get("remark") as string;

  // 创建付款记录
  await db.paymentRecord.create({
    data: {
      inviteCodeId,
      amount,
      extensionDays,
      remark: remark || null,
    },
  });

  // 更新授权令牌的过期时间
  const inviteCode = await db.inviteCode.findUnique({
    where: { id: inviteCodeId },
  });

  if (inviteCode) {
    const newExpiryDate = inviteCode.expiresAt
      ? new Date(inviteCode.expiresAt.getTime() + extensionDays * 24 * 60 * 60 * 1000)
      : new Date(Date.now() + extensionDays * 24 * 60 * 60 * 1000);

    await db.inviteCode.update({
      where: { id: inviteCodeId },
      data: {
        expiresAt: newExpiryDate,
        isPaid: true,
      },
    });
  }

  return json({ success: true, message: "付款记录添加成功" });
}

/**
 * 更新付款备注
 */
async function updatePaymentRemark(formData: FormData) {
  try {
    const paymentId = formData.get("paymentId") as string;
    const remark = formData.get("remark") as string;

    console.log("更新付款备注 - 参数:", { paymentId, remark });

    // 先检查付款记录是否存在
    const existingRecord = await db.paymentRecord.findUnique({
      where: { id: paymentId },
    });

    if (!existingRecord) {
      console.error("更新付款备注失败 - 付款记录不存在:", paymentId);
      return json({ error: "付款记录不存在" }, { status: 404 });
    }

    console.log("更新付款备注 - 当前备注:", existingRecord.remark);

    // 更新备注
    const updatedRecord = await db.paymentRecord.update({
      where: { id: paymentId },
      data: { remark: remark || null },
    });

    console.log("更新付款备注 - 更新后备注:", updatedRecord.remark);

    return json({ success: true, message: "备注更新成功" });
  } catch (error) {
    console.error("更新付款备注失败:", error);
    return json({
      error: "更新备注失败，请重试" + (error instanceof Error ? `: ${error.message}` : "")
    }, { status: 500 });
  }
}

/**
 * 导出账单
 */
async function exportBill(formData: FormData) {
  const startDate = formData.get("startDate") as string;
  const endDate = formData.get("endDate") as string;

  const payments = await db.paymentRecord.findMany({
    where: {
      paidAt: {
        gte: new Date(startDate),
        lte: new Date(endDate),
      },
    },
    include: {
      inviteCode: {
        select: {
          code: true,
          wechatId: true,
          product: {
            select: {
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      paidAt: 'desc',
    },
  });

  // 这里需要导入ExcelJS，但由于我们在服务端使用，需要在实际代码中处理
  // 这里只返回数据，实际导出功能在客户端处理
  return json({
    success: true,
    data: payments
  });
}

/**
 * 删除付款记录
 */
async function deletePayment(formData: FormData) {
  const paymentId = formData.get("paymentId") as string;

  // 获取付款记录信息
  const payment = await db.paymentRecord.findUnique({
    where: { id: paymentId },
    include: {
      inviteCode: true
    }
  });

  if (!payment) {
    return json({ error: "付款记录不存在" }, { status: 404 });
  }

  // 删除付款记录
  await db.paymentRecord.delete({
    where: { id: paymentId }
  });

  // 如果没有其他付款记录，将 isPaid 设置为 false
  const remainingPayments = await db.paymentRecord.count({
    where: { inviteCodeId: payment.inviteCodeId }
  });

  if (remainingPayments === 0) {
    await db.inviteCode.update({
      where: { id: payment.inviteCodeId },
      data: { isPaid: false }
    });
  }

  // 更新过期时间（减去被删除记录的延期天数）
  if (payment.inviteCode.expiresAt) {
    const newExpiryDate = new Date(payment.inviteCode.expiresAt.getTime() - payment.extensionDays * 24 * 60 * 60 * 1000);

    await db.inviteCode.update({
      where: { id: payment.inviteCodeId },
      data: { expiresAt: newExpiryDate }
    });
  }

  return json({ success: true, message: "付款记录已删除" });
}
