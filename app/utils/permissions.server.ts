import { redirect } from "@remix-run/node";
import { getUser } from "./session.server";

/**
 * 用户角色枚举
 */
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  AGENT = 'AGENT'
}

/**
 * 检查用户是否有管理员权限
 */
export async function requireAdmin(request: Request) {
  const user = await getUser(request);
  if (!user) {
    throw redirect("/login");
  }
  
  if (user.role !== UserRole.ADMIN) {
    throw new Response("Forbidden", { status: 403 });
  }
  
  return user;
}

/**
 * 检查用户是否有代理商或管理员权限
 */
export async function requireAgentOrAdmin(request: Request) {
  const user = await getUser(request);
  if (!user) {
    throw redirect("/login");
  }
  
  if (user.role !== UserRole.ADMIN && user.role !== UserRole.AGENT) {
    throw new Response("Forbidden", { status: 403 });
  }
  
  return user;
}

/**
 * 获取用户可访问的菜单列表
 */
export function getAccessibleMenus(userRole: string) {
  const allMenus = [
    { name: "控制台", to: "/dashboard", roles: [UserRole.ADMIN, UserRole.AGENT, UserRole.USER] },
    { name: "产品管理", to: "/products", roles: [UserRole.ADMIN] },
    { name: "授权令牌管理", to: "/", roles: [UserRole.ADMIN, UserRole.AGENT] },
    { name: "学员管理", to: "/students", roles: [UserRole.ADMIN] },
    { name: "用户管理", to: "/users", roles: [UserRole.ADMIN] },
    { name: "工单管理", to: "/tickets", roles: [UserRole.ADMIN] },
    { name: "菜单管理", to: "/menus", roles: [UserRole.ADMIN] },
    { name: "连接管理", to: "/connections", roles: [UserRole.ADMIN] },
    { name: "登录日志", to: "/login-logs", roles: [UserRole.ADMIN] },
    { name: "应用版本", to: "/app-versions", roles: [UserRole.ADMIN] }
  ];

  return allMenus.filter(menu => menu.roles.includes(userRole as UserRole));
}

/**
 * 检查用户是否可以访问指定路径
 */
export function canAccessPath(userRole: string, path: string): boolean {
  const accessibleMenus = getAccessibleMenus(userRole);
  return accessibleMenus.some(menu => path.startsWith(menu.to));
}

/**
 * 为代理商添加数据过滤条件
 * 代理商只能看到自己创建的数据
 */
export function addAgentDataFilter(user: any, baseWhere: any = {}) {
  if (user.role === UserRole.AGENT) {
    // 代理商只能看到自己创建的数据
    // 这里假设数据表中有 createdBy 字段，如果没有需要添加
    return {
      ...baseWhere,
      // 暂时注释掉，因为当前数据表可能没有 createdBy 字段
      // createdBy: user.id
    };
  }
  
  // 管理员可以看到所有数据
  return baseWhere;
}
