/**
 * 创建授权令牌表单组件
 */
import { useState, useEffect, useRef } from "react";
import { Form } from "@remix-run/react";
import { Switch } from "@headlessui/react";
import { CalendarIcon, ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import type { Product } from "~/types/inviteCode";

interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon?: string;
  parentId: string | null;
  sort: number;
  isEnabled: boolean;
  type: string;
  children?: MenuItem[];
}

interface CreateInviteCodeFormProps {
  onSubmit: () => void;
  isSubmitting: boolean;
  onClose: () => void;
  products: Product[];
}

export default function CreateInviteCodeForm({
  onSubmit,
  isSubmitting,
  onClose,
  products
}: CreateInviteCodeFormProps) {
  const [allowMultipleDevices, setAllowMultipleDevices] = useState(false);
  const [enforceAccountLimit, setEnforceAccountLimit] = useState(true);
  const [selectedMenuIds, setSelectedMenuIds] = useState<string[]>([]);
  const [systemMenus, setSystemMenus] = useState<MenuItem[]>([]);
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());
  const [showMenuAuth, setShowMenuAuth] = useState(false);

  const amountInputRef = useRef<HTMLInputElement>(null);
  const expiresAtInputRef = useRef<HTMLInputElement>(null);
  const cardTypeCustomRef = useRef<HTMLInputElement>(null);

  // 获取系统菜单
  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await fetch('/api/menus');
        const data = await response.json();
        if (data.success) {
          setSystemMenus(data.data);
        }
      } catch (error) {
        console.error('获取菜单失败:', error);
      }
    };
    fetchMenus();
  }, []);

  // 构建菜单树
  const buildMenuTree = (menus: MenuItem[]): MenuItem[] => {
    const menuMap = new Map<string, MenuItem>();
    const rootMenus: MenuItem[] = [];

    // 创建菜单映射
    menus.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [] });
    });

    // 构建树结构
    menus.forEach(menu => {
      const menuItem = menuMap.get(menu.id)!;
      if (menu.parentId) {
        const parent = menuMap.get(menu.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(menuItem);
        }
      } else {
        rootMenus.push(menuItem);
      }
    });

    return rootMenus.sort((a, b) => a.sort - b.sort);
  };

  const menuTree = buildMenuTree(systemMenus);

  // 处理菜单选择
  const handleMenuToggle = (menuId: string) => {
    setSelectedMenuIds(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedMenuIds.length === systemMenus.length) {
      setSelectedMenuIds([]);
    } else {
      setSelectedMenuIds(systemMenus.map(menu => menu.id));
    }
  };

  // 切换菜单展开状态
  const toggleMenuExpanded = (menuId: string) => {
    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  // 表单验证
  const handleFormSubmit = (e: React.FormEvent) => {
    if (selectedMenuIds.length === 0) {
      e.preventDefault();
      alert('请至少选择一个菜单权限');
      return;
    }
    onSubmit();
  };

  return (
    <Form method="post" onSubmit={handleFormSubmit} className="h-full flex flex-col">
      <input type="hidden" name="intent" value="create" />

      {/* 表单内容区域 - 可滚动 */}
      <div className="flex-1 overflow-y-auto px-1">
        <div className="space-y-6">
          {/* 基本信息区域 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
              <span className="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xs mr-2">1</span>
              基本信息
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="maxAccountCount"
            className="block text-sm font-medium text-gray-900"
          >
            授权账号数
          </label>
          <div className="mt-2 relative rounded-md shadow-sm">
            <input
              type="number"
              name="maxAccountCount"
              id="maxAccountCount"
              min="1"
              defaultValue="1"
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <span className="text-gray-500 sm:text-sm">个</span>
            </div>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            设置此授权令牌可以授权的账号数量
          </p>
        </div>

        <div>
          <label
            htmlFor="amount"
            className="block text-sm font-medium text-gray-900"
          >
            金额
          </label>
          <div className="mt-2 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="text-gray-500 sm:text-sm">¥</span>
            </div>
            <input
              type="number"
              name="amount"
              id="amount"
              ref={amountInputRef}
              min="0"
              step="0.01"
              placeholder="0.00"
              className="block w-full rounded-md border-0 py-1.5 pl-8 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </div>
          <div className="flex gap-2 mt-2">
            <button
              type="button"
              onClick={() => {
                if (amountInputRef.current) {
                  amountInputRef.current.value = '19.9';
                }
              }}
              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              ¥19.9
            </button>
            <button
              type="button"
              onClick={() => {
                if (amountInputRef.current) {
                  amountInputRef.current.value = '199';
                }
              }}
              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              ¥199
            </button>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            设置金额将自动创建付款记录并标记为已付费
          </p>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <label
              htmlFor="allowMultipleDevices"
              className="block text-sm font-medium text-gray-900"
            >
              允许多设备登录
            </label>
            <Switch
              id="allowMultipleDevices"
              name="allowMultipleDevices"
              checked={allowMultipleDevices}
              onChange={setAllowMultipleDevices}
              className={`${
                allowMultipleDevices ? 'bg-indigo-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
            >
              <span className="sr-only">允许多设备登录</span>
              <span
                className={`${
                  allowMultipleDevices ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
              />
            </Switch>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            启用后，此授权令牌可以在多台设备同时登录，但总账号数不超过授权数量
          </p>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <label
              htmlFor="enforceAccountLimit"
              className="block text-sm font-medium text-gray-900"
            >
              根据可使用数限制创建账号
            </label>
            <Switch
              id="enforceAccountLimit"
              name="enforceAccountLimit"
              checked={enforceAccountLimit}
              onChange={setEnforceAccountLimit}
              className={`${
                enforceAccountLimit ? 'bg-indigo-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
            >
              <span className="sr-only">根据可使用数限制创建账号</span>
              <span
                className={`${
                  enforceAccountLimit ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
              />
            </Switch>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            启用后，用户创建TikTok账号时会受到可使用数量的限制；关闭后，用户可以无限创建账号
          </p>
        </div>

        <div>
          <label htmlFor="expiresAt" className="block text-sm font-medium text-gray-900">
            套餐类型
          </label>

          <div className="mt-2 grid grid-cols-4 gap-3">
            <div>
              <input
                type="radio"
                id="cardType-day"
                name="cardType"
                value="day"
                className="sr-only peer"
              />
              <label
                htmlFor="cardType-day"
                className="flex justify-center items-center h-10 w-full text-sm font-medium rounded-lg border border-gray-300 cursor-pointer focus:outline-none peer-checked:border-indigo-600 peer-checked:text-indigo-600 hover:bg-gray-50 peer-checked:bg-indigo-50"
              >
                天卡
              </label>
            </div>
            <div>
              <input
                type="radio"
                id="cardType-month"
                name="cardType"
                value="month"
                className="sr-only peer"
              />
              <label
                htmlFor="cardType-month"
                className="flex justify-center items-center h-10 w-full text-sm font-medium rounded-lg border border-gray-300 cursor-pointer focus:outline-none peer-checked:border-indigo-600 peer-checked:text-indigo-600 hover:bg-gray-50 peer-checked:bg-indigo-50"
              >
                月卡
              </label>
            </div>
            <div>
              <input
                type="radio"
                id="cardType-year"
                name="cardType"
                value="year"
                className="sr-only peer"
              />
              <label
                htmlFor="cardType-year"
                className="flex justify-center items-center h-10 w-full text-sm font-medium rounded-lg border border-gray-300 cursor-pointer focus:outline-none peer-checked:border-indigo-600 peer-checked:text-indigo-600 hover:bg-gray-50 peer-checked:bg-indigo-50"
              >
                年卡
              </label>
            </div>
            <div>
              <input
                type="radio"
                id="cardType-custom"
                name="cardType"
                value="custom"
                defaultChecked
                ref={cardTypeCustomRef}
                className="sr-only peer"
              />
              <label
                htmlFor="cardType-custom"
                className="flex justify-center items-center h-10 w-full text-sm font-medium rounded-lg border border-gray-300 cursor-pointer focus:outline-none peer-checked:border-indigo-600 peer-checked:text-indigo-600 hover:bg-gray-50 peer-checked:bg-indigo-50"
              >
                自定义
              </label>
            </div>
          </div>

          <div className="mt-1 text-xs text-gray-500">
            <span className="block">• 天卡：有效期 1 天</span>
            <span className="block">• 月卡：有效期 30 天</span>
            <span className="block">• 年卡：有效期 366 天</span>
            <span className="block">• 自定义：手动设置过期时间</span>
          </div>
        </div>

        <div>
          <label htmlFor="wechatId" className="block text-sm font-medium text-gray-900">
            微信号
          </label>
          <div className="mt-2">
            <input
              type="text"
              name="wechatId"
              id="wechatId"
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              placeholder="请输入用户的微信号"
            />
          </div>
          <p className="mt-2 text-sm text-gray-500">
            用于记录和识别使用此授权令牌的用户
          </p>
        </div>

        <div>
          <label htmlFor="expiresAt" className="block text-sm font-medium text-gray-900">
            过期时间（自定义）
          </label>

          <div className="flex flex-wrap gap-2 mt-2">
            {[
              { days: 1, label: '1天' },
              { days: 3, label: '3天' },
              { days: 7, label: '7天' },
              { days: 30, label: '1个月' },
              { days: 90, label: '3个月' },
              { days: 180, label: '6个月' },
              { days: 365, label: '1年' },
            ].map(({ days, label }) => (
              <button
                key={days}
                type="button"
                onClick={() => {
                  // 创建一个新的日期对象
                  const date = new Date();
                  // 先加上天数
                  date.setDate(date.getDate() + days);
                  // 设置为当天的00:00:00，确保整天计算
                  date.setHours(0, 0, 0, 0);

                  if (expiresAtInputRef.current) {
                    // 使用本地时间格式，确保与datetime-local输入框兼容
                    const yyyy = date.getFullYear();
                    const MM = String(date.getMonth() + 1).padStart(2, '0');
                    const dd = String(date.getDate()).padStart(2, '0');
                    // 固定时间为00:00:00
                    expiresAtInputRef.current.value = `${yyyy}-${MM}-${dd}T00:00:00`;
                  }

                  // 自动选择自定义套餐类型
                  if (cardTypeCustomRef.current) {
                    cardTypeCustomRef.current.checked = true;
                  }
                }}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                {label}
              </button>
            ))}
            <button
              type="button"
              onClick={() => {
                if (expiresAtInputRef.current) {
                  expiresAtInputRef.current.value = '';
                }
              }}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-red-600 bg-white hover:bg-red-50 hover:border-red-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              清除
            </button>
          </div>

          <div className="mt-3">
            <div className="relative rounded-md shadow-sm">
              <input
                type="datetime-local"
                id="expiresAt"
                name="expiresAt"
                ref={expiresAtInputRef}
                step="1"
                className="block w-full rounded-md border-gray-300 pr-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                onClick={() => {
                  // 自动选择自定义套餐类型
                  if (cardTypeCustomRef.current) {
                    cardTypeCustomRef.current.checked = true;
                  }
                }}
              />
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <CalendarIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              选择&quot;自定义&quot;套餐类型时使用。不设置则永久有效。
            </p>
          </div>
        </div>
      </div>

      <div>
        <label htmlFor="productSelection" className="block text-sm font-medium text-gray-900">
          所属产品
        </label>
        <div id="productSelection" className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {products.map((product, index) => (
            <div
              key={product.id}
              className="relative flex items-center"
            >
              <input
                type="radio"
                id={`product-${product.id}`}
                name="productId"
                value={product.id}
                defaultChecked={index === 0} // 默认选中第一个产品
                required
                className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label
                htmlFor={`product-${product.id}`}
                className="ml-3 block text-sm font-medium text-gray-700 cursor-pointer"
                aria-label={`选择产品: ${product.name} 版本 ${product.version}`}
              >
                <div className="flex flex-col">
                  <span className="font-medium text-gray-900">
                    {product.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    版本 {product.version}
                  </span>
                </div>
              </label>
            </div>
          ))}
        </div>
        <p className="mt-2 text-sm text-gray-500">
          选择授权令牌所属的产品
        </p>
      </div>

      <div>
        <label htmlFor="remark" className="block text-sm font-medium text-gray-900">
          备注信息
        </label>
        <div className="mt-2">
          <textarea
            id="remark"
            name="remark"
            rows={3}
            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            placeholder="输入关于此授权令牌的备注信息"
          />
        </div>
        <p className="mt-2 text-sm text-gray-500">
          可以记录用户信息、用途等内容
        </p>
      </div>

      {/* 菜单授权区域 */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-sm font-medium text-gray-900">菜单授权</h3>
            <p className="text-sm text-gray-500">选择此授权令牌可以访问的菜单项</p>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleSelectAll}
              className="text-sm text-indigo-600 hover:text-indigo-500"
            >
              {selectedMenuIds.length === systemMenus.length ? '清除全部' : '选择全部'}
            </button>
          </div>
        </div>

        {systemMenus.length > 0 ? (
          <div className="border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
            <div className="space-y-2">
              {menuTree.map((menu) => (
                <MenuTreeItem
                  key={menu.id}
                  menu={menu}
                  selectedMenuIds={selectedMenuIds}
                  expandedMenus={expandedMenus}
                  onToggle={handleMenuToggle}
                  onExpandToggle={toggleMenuExpanded}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="border border-gray-200 rounded-lg p-4 text-center text-gray-500">
            正在加载菜单...
          </div>
        )}

        {/* 隐藏字段用于提交选中的菜单 */}
        {selectedMenuIds.map(menuId => (
          <input
            key={menuId}
            type="hidden"
            name="menuIds[]"
            value={menuId}
          />
        ))}
      </div>

      <div className="mt-5 flex justify-end gap-2">
        <button
          type="button"
          onClick={onClose}
          className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className={`inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm ${
            isSubmitting
              ? "bg-indigo-400 cursor-not-allowed"
              : "bg-indigo-600 hover:bg-indigo-700"
          }`}
        >
          {isSubmitting ? "创建中..." : "创建授权令牌"}
        </button>
      </div>
    </Form>
  );
}

// 菜单树项组件
interface MenuTreeItemProps {
  menu: MenuItem;
  selectedMenuIds: string[];
  expandedMenus: Set<string>;
  onToggle: (menuId: string) => void;
  onExpandToggle: (menuId: string) => void;
  level?: number;
}

function MenuTreeItem({
  menu,
  selectedMenuIds,
  expandedMenus,
  onToggle,
  onExpandToggle,
  level = 0
}: MenuTreeItemProps) {
  const hasChildren = menu.children && menu.children.length > 0;
  const isExpanded = expandedMenus.has(menu.id);
  const isSelected = selectedMenuIds.includes(menu.id);

  return (
    <div>
      <div
        className="flex items-center py-2 px-2 hover:bg-gray-50 rounded"
        style={{ paddingLeft: `${level * 20 + 8}px` }}
      >
        {hasChildren && (
          <button
            type="button"
            onClick={() => onExpandToggle(menu.id)}
            className="mr-2 p-1 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
        )}

        {!hasChildren && <div className="w-6 mr-2" />}

        <label className="flex items-center flex-1 cursor-pointer">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onToggle(menu.id)}
            className="mr-3 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <div className="flex items-center">
            <span className="text-sm text-gray-900">{menu.name}</span>
            {menu.type === 'BUTTON' && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                按钮
              </span>
            )}
            {menu.type === 'DIRECTORY' && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                目录
              </span>
            )}
          </div>
        </label>
      </div>

      {hasChildren && isExpanded && (
        <div>
          {menu.children!.map((child) => (
            <MenuTreeItem
              key={child.id}
              menu={child}
              selectedMenuIds={selectedMenuIds}
              expandedMenus={expandedMenus}
              onToggle={onToggle}
              onExpandToggle={onExpandToggle}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
