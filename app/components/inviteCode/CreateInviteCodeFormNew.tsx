/**
 * 创建授权令牌表单组件 - 重新设计版本
 */
import { useState, useEffect, useRef } from "react";
import { Form } from "@remix-run/react";
import { Switch } from "@headlessui/react";
import { CalendarIcon, ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import type { Product } from "~/types/inviteCode";

interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon?: string;
  parentId: string | null;
  sort: number;
  isEnabled: boolean;
  type: string;
  children?: MenuItem[];
}

interface CreateInviteCodeFormProps {
  onSubmit: () => void;
  isSubmitting: boolean;
  onClose: () => void;
  products: Product[];
}

export default function CreateInviteCodeForm({
  onSubmit,
  isSubmitting,
  onClose,
  products
}: CreateInviteCodeFormProps) {
  const [allowMultipleDevices, setAllowMultipleDevices] = useState(false);
  const [enforceAccountLimit, setEnforceAccountLimit] = useState(true);
  const [selectedMenuIds, setSelectedMenuIds] = useState<string[]>([]);
  const [systemMenus, setSystemMenus] = useState<MenuItem[]>([]);
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  const amountInputRef = useRef<HTMLInputElement>(null);
  const expiresAtInputRef = useRef<HTMLInputElement>(null);
  const cardTypeCustomRef = useRef<HTMLInputElement>(null);

  // 获取系统菜单
  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await fetch('/api/menus');
        const data = await response.json();
        if (data.success) {
          setSystemMenus(data.data);
        }
      } catch (error) {
        console.error('获取菜单失败:', error);
      }
    };
    fetchMenus();
  }, []);

  // 构建菜单树
  const buildMenuTree = (menus: MenuItem[]): MenuItem[] => {
    const menuMap = new Map<string, MenuItem>();
    const rootMenus: MenuItem[] = [];

    // 创建菜单映射
    menus.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [] });
    });

    // 构建树结构
    menus.forEach(menu => {
      const menuItem = menuMap.get(menu.id)!;
      if (menu.parentId) {
        const parent = menuMap.get(menu.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(menuItem);
        }
      } else {
        rootMenus.push(menuItem);
      }
    });

    return rootMenus.sort((a, b) => a.sort - b.sort);
  };

  const menuTree = buildMenuTree(systemMenus);

  // 处理菜单选择
  const handleMenuToggle = (menuId: string) => {
    setSelectedMenuIds(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedMenuIds.length === systemMenus.length) {
      setSelectedMenuIds([]);
    } else {
      setSelectedMenuIds(systemMenus.map(menu => menu.id));
    }
  };

  // 切换菜单展开状态
  const toggleMenuExpanded = (menuId: string) => {
    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  // 表单验证
  const handleFormSubmit = (e: React.FormEvent) => {
    // 检查是否选择了产品
    const formData = new FormData(e.target as HTMLFormElement);
    const productId = formData.get('productId');

    if (!productId) {
      e.preventDefault();
      alert('请选择产品');
      return;
    }

    if (selectedMenuIds.length === 0) {
      e.preventDefault();
      alert('请至少选择一个菜单权限');
      return;
    }
    onSubmit();
  };

  return (
    <div className="h-full flex flex-col">
      <Form method="post" onSubmit={handleFormSubmit} className="flex-1 flex flex-col">
        <input type="hidden" name="intent" value="create" />

        {/* 表单内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：基本信息 */}
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                  <span className="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xs mr-3">1</span>
                  基本信息
                </h3>
                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label htmlFor="maxAccountCount" className="block text-sm font-medium text-gray-900">
                        授权账号数 <span className="text-red-500">*</span>
                      </label>
                      <div className="mt-2 relative rounded-md shadow-sm">
                        <input
                          type="number"
                          name="maxAccountCount"
                          id="maxAccountCount"
                          min="1"
                          defaultValue="1"
                          required
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <span className="text-gray-500 sm:text-sm">个</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="amount" className="block text-sm font-medium text-gray-900">
                        金额
                      </label>
                      <div className="mt-2 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <span className="text-gray-500 sm:text-sm">¥</span>
                        </div>
                        <input
                          type="number"
                          name="amount"
                          id="amount"
                          ref={amountInputRef}
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          className="block w-full rounded-md border-0 py-1.5 pl-8 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                      <div className="flex gap-2 mt-2">
                        <button
                          type="button"
                          onClick={() => {
                            if (amountInputRef.current) {
                              amountInputRef.current.value = "19.90";
                            }
                          }}
                          className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
                        >
                          ¥19.90
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            if (amountInputRef.current) {
                              amountInputRef.current.value = "39.90";
                            }
                          }}
                          className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
                        >
                          ¥39.90
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* 产品选择 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-3">
                      选择产品 <span className="text-red-500">*</span>
                    </label>
                    <div className="space-y-2">
                      {products.map((product) => (
                        <div key={product.id}>
                          <input
                            type="radio"
                            id={`product-${product.id}`}
                            name="productId"
                            value={product.id}
                            required
                            className="sr-only peer"
                          />
                          <label
                            htmlFor={`product-${product.id}`}
                            className="flex justify-between items-center p-3 text-sm font-medium rounded-lg border border-gray-300 cursor-pointer focus:outline-none peer-checked:border-indigo-600 peer-checked:text-indigo-600 hover:bg-gray-50 peer-checked:bg-indigo-50"
                          >
                            <div>
                              <span className="font-medium text-gray-900">{product.name}</span>
                              <span className="text-xs text-gray-500 block">版本 {product.version}</span>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 其他设置 */}
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="wechatId" className="block text-sm font-medium text-gray-900">
                        微信号
                      </label>
                      <input
                        type="text"
                        name="wechatId"
                        id="wechatId"
                        className="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        placeholder="请输入用户的微信号"
                      />
                    </div>

                    <div>
                      <label htmlFor="remark" className="block text-sm font-medium text-gray-900">
                        备注信息
                      </label>
                      <input
                        type="text"
                        name="remark"
                        id="remark"
                        className="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        placeholder="输入备注信息"
                      />
                    </div>
                  </div>

                  {/* 开关设置 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <label className="text-sm font-medium text-gray-900">允许多设备登录</label>
                        <p className="text-sm text-gray-500">是否允许同一个授权令牌在多个设备上同时使用</p>
                      </div>
                      <Switch
                        checked={allowMultipleDevices}
                        onChange={setAllowMultipleDevices}
                        className={`${
                          allowMultipleDevices ? 'bg-indigo-600' : 'bg-gray-200'
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ml-4`}
                      >
                        <span
                          className={`${
                            allowMultipleDevices ? 'translate-x-6' : 'translate-x-1'
                          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
                        />
                      </Switch>
                      <input type="hidden" name="allowMultipleDevices" value={allowMultipleDevices ? "on" : "off"} />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <label className="text-sm font-medium text-gray-900">根据可使用数限制创建账号</label>
                        <p className="text-sm text-gray-500">是否根据设置的账号数量限制用户创建TikTok账号</p>
                      </div>
                      <Switch
                        checked={enforceAccountLimit}
                        onChange={setEnforceAccountLimit}
                        className={`${
                          enforceAccountLimit ? 'bg-indigo-600' : 'bg-gray-200'
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ml-4`}
                      >
                        <span
                          className={`${
                            enforceAccountLimit ? 'translate-x-6' : 'translate-x-1'
                          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
                        />
                      </Switch>
                      <input type="hidden" name="enforceAccountLimit" value={enforceAccountLimit ? "on" : "off"} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：菜单权限 */}
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                  <span className="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-xs mr-3">2</span>
                  菜单权限设置 <span className="text-red-500 ml-1">*</span>
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      已选择 {selectedMenuIds.length} / {systemMenus.length} 个菜单
                    </span>
                    <button
                      type="button"
                      onClick={handleSelectAll}
                      className="text-sm text-indigo-600 hover:text-indigo-500"
                    >
                      {selectedMenuIds.length === systemMenus.length ? '清除全部' : '选择全部'}
                    </button>
                  </div>

                  {systemMenus.length > 0 ? (
                    <div className="border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                      <div className="space-y-2">
                        {menuTree.map((menu) => (
                          <MenuTreeItem
                            key={menu.id}
                            menu={menu}
                            selectedMenuIds={selectedMenuIds}
                            expandedMenus={expandedMenus}
                            onToggle={handleMenuToggle}
                            onExpandToggle={toggleMenuExpanded}
                          />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="border border-gray-200 rounded-lg p-8 text-center text-gray-500">
                      正在加载菜单...
                    </div>
                  )}

                  {/* 隐藏字段用于提交选中的菜单 */}
                  {selectedMenuIds.map(menuId => (
                    <input
                      key={menuId}
                      type="hidden"
                      name="menuIds[]"
                      value={menuId}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮区域 */}
        <div className="flex-shrink-0 border-t border-gray-200 pt-6 mt-8">
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              取消
            </button>

            <button
              type="submit"
              disabled={isSubmitting || selectedMenuIds.length === 0}
              className={`inline-flex justify-center rounded-md border border-transparent px-6 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                isSubmitting || selectedMenuIds.length === 0
                  ? "bg-indigo-400 cursor-not-allowed"
                  : "bg-indigo-600 hover:bg-indigo-700"
              }`}
            >
              {isSubmitting ? "创建中..." : "创建授权令牌"}
            </button>
          </div>
        </div>
      </Form>
    </div>
  );
}

// 菜单树项组件
interface MenuTreeItemProps {
  menu: MenuItem;
  selectedMenuIds: string[];
  expandedMenus: Set<string>;
  onToggle: (menuId: string) => void;
  onExpandToggle: (menuId: string) => void;
  level?: number;
}

function MenuTreeItem({
  menu,
  selectedMenuIds,
  expandedMenus,
  onToggle,
  onExpandToggle,
  level = 0
}: MenuTreeItemProps) {
  const hasChildren = menu.children && menu.children.length > 0;
  const isExpanded = expandedMenus.has(menu.id);
  const isSelected = selectedMenuIds.includes(menu.id);

  return (
    <div>
      <div
        className="flex items-center py-2 px-2 hover:bg-gray-50 rounded"
        style={{ paddingLeft: `${level * 20 + 8}px` }}
      >
        {hasChildren && (
          <button
            type="button"
            onClick={() => onExpandToggle(menu.id)}
            className="mr-2 p-1 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
        )}

        {!hasChildren && <div className="w-6 mr-2" />}

        <label className="flex items-center flex-1 cursor-pointer">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onToggle(menu.id)}
            className="mr-3 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <div className="flex items-center">
            <span className="text-sm text-gray-900">{menu.name}</span>
            {menu.type === 'BUTTON' && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                按钮
              </span>
            )}
            {menu.type === 'DIRECTORY' && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                目录
              </span>
            )}
          </div>
        </label>
      </div>

      {hasChildren && isExpanded && (
        <div>
          {menu.children!.map((child) => (
            <MenuTreeItem
              key={child.id}
              menu={child}
              selectedMenuIds={selectedMenuIds}
              expandedMenus={expandedMenus}
              onToggle={onToggle}
              onExpandToggle={onExpandToggle}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
