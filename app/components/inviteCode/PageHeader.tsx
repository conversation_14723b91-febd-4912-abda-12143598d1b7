/**
 * 授权令牌页面头部组件
 */
import { Link } from "@remix-run/react";

interface PageHeaderProps {
  totalIncome: number;
}

export default function PageHeader({ totalIncome }: PageHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 gap-4">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">授权令牌管理</h1>
        <p className="mt-1 text-sm text-gray-500">
          管理和监控授权令牌使用情况
        </p>
      </div>
      <div className="flex items-center gap-4">
        
        <div className="text-left sm:text-right">
          <div className="text-sm text-gray-500">总收入</div>
          <div className="text-xl sm:text-2xl font-bold text-gray-900">
            ¥{(typeof totalIncome === 'number' ? totalIncome : Number(totalIncome)).toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
}
