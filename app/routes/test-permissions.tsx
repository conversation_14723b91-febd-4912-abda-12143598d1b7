import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import AdminLayout from "~/components/Layout";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);
  
  return json({
    user,
    timestamp: new Date().toISOString()
  });
}

export default function TestPermissions() {
  const { user, timestamp } = useLoaderData<typeof loader>();
  
  return (
    <AdminLayout user={{ name: user?.username || "用户", role: user?.role }}>
      <div className="min-h-screen bg-gray-50 py-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow rounded-lg p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">权限测试页面</h1>
            
            <div className="space-y-4">
              <div>
                <h2 className="text-lg font-semibold text-gray-700">用户信息</h2>
                <div className="mt-2 bg-gray-50 p-4 rounded">
                  <p><strong>用户ID:</strong> {user?.id}</p>
                  <p><strong>用户名:</strong> {user?.username}</p>
                  <p><strong>角色:</strong> {user?.role}</p>
                  <p><strong>访问时间:</strong> {timestamp}</p>
                </div>
              </div>
              
              <div>
                <h2 className="text-lg font-semibold text-gray-700">权限说明</h2>
                <div className="mt-2 bg-blue-50 p-4 rounded">
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li><strong>ADMIN (管理员):</strong> 可以访问所有菜单和功能</li>
                    <li><strong>AGENT (代理商):</strong> 只能访问"控制台"和"授权令牌管理"，只能看到自己创建的数据</li>
                    <li><strong>USER (普通用户):</strong> 只能访问"控制台"</li>
                  </ul>
                </div>
              </div>
              
              <div>
                <h2 className="text-lg font-semibold text-gray-700">测试链接</h2>
                <div className="mt-2 space-y-2">
                  <div>
                    <a href="/" className="text-blue-600 hover:text-blue-800 underline">
                      授权令牌管理 (ADMIN/AGENT可访问)
                    </a>
                  </div>
                  <div>
                    <a href="/users" className="text-blue-600 hover:text-blue-800 underline">
                      用户管理 (仅ADMIN可访问)
                    </a>
                  </div>
                  <div>
                    <a href="/products" className="text-blue-600 hover:text-blue-800 underline">
                      产品管理 (仅ADMIN可访问)
                    </a>
                  </div>
                  <div>
                    <a href="/dashboard" className="text-blue-600 hover:text-blue-800 underline">
                      控制台 (所有角色可访问)
                    </a>
                  </div>
                </div>
              </div>
              
              {user?.role === 'AGENT' && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-700">代理商提示</h2>
                  <div className="mt-2 bg-yellow-50 p-4 rounded">
                    <p className="text-sm text-yellow-800">
                      作为代理商，您只能看到自己创建的授权令牌。如果您是新用户，请先创建一些授权令牌来测试数据隔离功能。
                    </p>
                  </div>
                </div>
              )}
              
              {user?.role === 'ADMIN' && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-700">管理员提示</h2>
                  <div className="mt-2 bg-green-50 p-4 rounded">
                    <p className="text-sm text-green-800">
                      作为管理员，您可以访问所有功能和查看所有数据。您可以在用户管理页面创建代理商账号来测试权限控制。
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
