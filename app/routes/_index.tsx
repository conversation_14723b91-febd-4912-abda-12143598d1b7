/**
 * 授权令牌管理主页面
 * 重构后的版本，将大文件拆分为更小的模块
 */
import type { MetaFunction } from "@remix-run/node";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigation, useActionData, useSearchParams, useSubmit } from "@remix-run/react";
import { useState, useEffect } from "react";
import AdminLayout from "~/components/Layout";
import Modal from "~/components/Modal";
import ConfirmDialog from "~/components/ConfirmDialog";
import EditDialog from "~/components/EditDialog";
import RenewalDialog from "~/components/RenewalDialog";
import BatchRenewalDialog from "~/components/BatchRenewalDialog";
import ShareDialog from "~/components/ShareDialog";
import Toast from "~/components/Toast";
import PaymentDialog from "~/components/PaymentDialog";
import ColumnSelector from "~/components/ColumnSelector";
import AccountListDialog from "~/components/AccountListDialog";
import MenuAuthDialog from "~/components/MenuAuthDialog";

// 导入重构后的组件
import PageHeader from "~/components/inviteCode/PageHeader";
import SearchAndFilter from "~/components/inviteCode/SearchAndFilter";
import TabNavigation from "~/components/inviteCode/TabNavigation";
import InviteCodeTable from "~/components/inviteCode/InviteCodeTable";
import CreateInviteCodeForm from "~/components/inviteCode/CreateInviteCodeForm";

// 导入服务和工具函数
import { loadInviteCodes, handleInviteCodeAction } from "~/services/inviteCodeService";
import { formatShareText } from "~/utils/inviteCodeUtils";

// 导入类型
import type {
  EditInviteCodeData,
  RenewInviteCodeData,
  MenuAuthData,
  PayingCodeData,
  AccountListData,
  ColumnConfig
} from "~/types/inviteCode";
import { MenuType } from "~/types/inviteCode";

// 元数据
export const meta: MetaFunction = () => {
  return [
    { title: "小鹿推流助手" },
    { name: "description", content: "小鹿推流助手" },
  ];
};

// 加载器函数
export const loader = loadInviteCodes;

// Action函数
export const action = handleInviteCodeAction;

// 定义列配置
const defaultColumns: ColumnConfig[] = [
  { id: 'code', title: '授权令牌', defaultVisible: true },
  { id: 'wechatId', title: '微信号', defaultVisible: true },
  { id: 'product', title: '产品', defaultVisible: true },
  { id: 'maxAccounts', title: '账号数', defaultVisible: true },
  { id: 'devices', title: '设备', defaultVisible: true },
  { id: 'multiDevice', title: '多设备', defaultVisible: true },
  { id: 'expiry', title: '过期时间', defaultVisible: true },
  { id: 'remark', title: '备注', defaultVisible: true },
  { id: 'totalPayment', title: '总付款金额', defaultVisible: true },
  { id: 'createdAt', title: '创建时间', defaultVisible: true },
  { id: 'actions', title: '操作', defaultVisible: true }
];

export default function Index() {
  const { inviteCodes, user, pagination, products, totalIncome } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const actionData = useActionData<typeof action>();
  const [searchParams] = useSearchParams();
  const isSubmitting = navigation.state === "submitting";
  const submit = useSubmit();

  // 状态管理
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [editingCode, setEditingCode] = useState<EditInviteCodeData | null>(null);
  const [renewingCode, setRenewingCode] = useState<RenewInviteCodeData | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [clearingDeviceId, setClearingDeviceId] = useState<string | null>(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [shareText, setShareText] = useState("");
  const [showError, setShowError] = useState(false);
  const [activeTab, setActiveTab] = useState<'active' | 'deleted'>(
    searchParams.get('tab') === 'deleted' ? 'deleted' : 'active'
  );
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState<'wechatId' | 'code'>('wechatId');
  const [payingCode, setPayingCode] = useState<PayingCodeData | null>(null);
  const [togglePaidId, setTogglePaidId] = useState<string | null>(null);
  const [allowMultipleDevices, setAllowMultipleDevices] = useState(false);
  const [currentShareCode, setCurrentShareCode] = useState('');
  const [copySuccess, setCopySuccess] = useState<{id: string, success: boolean} | null>(null);
  const [showAccountList, setShowAccountList] = useState(false);
  const [selectedInviteCodeAccounts, setSelectedInviteCodeAccounts] = useState<AccountListData | null>(null);
  const [menuAuthCode, setMenuAuthCode] = useState<MenuAuthData | null>(null);
  const [systemMenus, setSystemMenus] = useState<any[]>([]);

  // 修改列显示状态的初始化
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    defaultColumns.filter(c => c.defaultVisible).map(c => c.id)
  );

  // 批量续期相关状态
  const [selectedInviteCodes, setSelectedInviteCodes] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showBatchRenewalDialog, setShowBatchRenewalDialog] = useState(false);

  // 使用 useEffect 处理 localStorage
  useEffect(() => {
    const saved = localStorage.getItem('visibleColumns');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          setVisibleColumns(parsed);
        }
      } catch (e) {
        console.error('Failed to parse visibleColumns from localStorage');
      }
    }
  }, []);

  // 获取系统中所有启用状态的菜单
  useEffect(() => {
    const fetchMenus = async () => {
      try {
        // 先尝试添加连接管理菜单
        try {
          await fetch('/api/add-connection-menu');
        } catch (error) {
          console.error('添加连接管理菜单失败:', error);
        }

      // 更新连接管理菜单路径
      try {
        await fetch('/api/update-connection-menu');
      } catch (error) {
        console.error('更新连接管理菜单路径失败:', error);
      }

        // 获取所有启用状态的菜单
        const response = await fetch('/api/menus');
        const data = await response.json();

        if (data.success) {
          setSystemMenus(data.data);
        } else {
          console.error('获取菜单失败:', data.error);
          // 使用默认菜单作为备选
          setSystemMenus([
            {
              id: "dashboard",
              name: "控制台",
              path: "/dashboard",
              icon: "DashboardIcon",
              parentId: null,
              sort: 0,
              isEnabled: true,
              type: MenuType.MENU
            },
            {
              id: "products",
              name: "产品管理",
              path: "/products",
              icon: "ShoppingBagIcon",
              parentId: null,
              sort: 1,
              isEnabled: true,
              type: MenuType.MENU
            },
            {
              id: "invite-codes",
              name: "授权令牌管理",
              path: "/",
              icon: "KeyIcon",
              parentId: null,
              sort: 2,
              isEnabled: true,
              type: MenuType.MENU
            },
            {
              id: "students",
              name: "学员管理",
              path: "/students",
              icon: "UserGroupIcon",
              parentId: null,
              sort: 3,
              isEnabled: true,
              type: MenuType.MENU
            },
            {
              id: "users",
              name: "用户管理",
              path: "/users",
              icon: "UserIcon",
              parentId: null,
              sort: 4,
              isEnabled: true,
              type: MenuType.MENU
            }
          ]);
        }
      } catch (error) {
        console.error('获取菜单出错:', error);
      }
    };

    fetchMenus();
  }, []);

  // 处理操作结果
  useEffect(() => {
    if (navigation.state === "idle") {
      if (actionData?.error) {
        setShowError(true);
        setShowToast(false);
      } else if (navigation.formData?.get("intent") === "delete" && !actionData?.error) {
        setToastMessage("授权令牌已删除");
        setShowToast(true);
      }
    }
  }, [navigation.state, actionData, navigation.formData]);

  // 处理自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const timer = setInterval(() => {
      window.location.reload();
    }, refreshInterval * 1000);

    return () => clearInterval(timer);
  }, [autoRefresh, refreshInterval]);

  // 处理操作成功消息
  useEffect(() => {
    if (navigation.state === "idle" && actionData?.success) {
      setShowToast(true);
      setToastMessage(actionData.message || "操作成功");

      if (actionData.message?.includes("付款")) {
        setPayingCode(null);
      }
    }
  }, [navigation.state, actionData]);

  // 处理列显示切换
  const handleColumnToggle = (columnId: string) => {
    const newVisibleColumns = visibleColumns.includes(columnId)
      ? visibleColumns.filter(id => id !== columnId)
      : [...visibleColumns, columnId];
    setVisibleColumns(newVisibleColumns);
    localStorage.setItem('visibleColumns', JSON.stringify(newVisibleColumns));
  };

  // 处理删除
  const handleDelete = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "delete");
    submit(formData, { method: "post" });
    setDeleteId(null);
  };

  // 处理编辑
  const handleEdit = (data: {
    wechatId: string;
    remark: string;
    maxAccountCount: number;
    expiresAt: Date | null;
    productId: string;
    allowMultipleDevices: boolean;
    enforceAccountLimit: boolean;
  }) => {
    if (editingCode) {
      const formData = new FormData();
      formData.append("id", editingCode.id);
      formData.append("wechatId", data.wechatId);
      formData.append("remark", data.remark);
      formData.append("maxAccountCount", data.maxAccountCount.toString());
      formData.append("expiresAt", data.expiresAt ? data.expiresAt.toString() : '');
      formData.append("productId", data.productId);
      formData.append("allowMultipleDevices", data.allowMultipleDevices ? "on" : "off");
      // enforceAccountLimit字段暂时不添加，等待数据库迁移完成
      formData.append("intent", "edit");
      submit(formData, { method: "post" });
      setEditingCode(null);
    }
  };

  // 处理续期
  const handleRenewal = (data: {
    newExpiry: Date;
    newCount: number;
    fee: number;
    remark: string;
    productId: string;
  }) => {
    if (renewingCode) {
      const formData = new FormData();
      formData.append("id", renewingCode.id);
      formData.append("newExpiry", data.newExpiry.toISOString());
      formData.append("newCount", data.newCount.toString());
      formData.append("fee", data.fee.toString());
      formData.append("remark", data.remark);
      formData.append("productId", data.productId);
      formData.append("intent", "renew");
      submit(formData, { method: "post" });
      setRenewingCode(null);
    }
  };

  // 处理清除设备
  const handleClearDevice = (id: string) => {
    const formData = new FormData();
    formData.append("intent", "clearDevice");
    formData.append("id", id);

    submit(formData, { method: "post" });
    setToastMessage("设备已清除，用户需要重新登录");
    setShowToast(true);
    setClearingDeviceId(null);
  };

  // 处理分享
  const handleShare = (code: {
    code: string;
    maxAccountCount: number;
    expiresAt: Date | null;
    product: {
      name: string;
      version: string;
    };
  }) => {
    const text = formatShareText(code);
    setShareText(text);
    setShareDialogOpen(true);
    // 保存当前分享的邀请码，便于模板编辑
    setCurrentShareCode(code.code);
  };

  // 处理恢复
  const handleRestore = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "restore");
    submit(formData, { method: "post" });
  };

  // 处理永久删除
  const handlePermanentDelete = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "permanentDelete");
    submit(formData, { method: "post" });
  };

  // 处理切换付费状态
  const handleTogglePaid = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "toggle-paid");
    submit(formData, { method: "post" });
    setTogglePaidId(null);
  };

  // 直接复制分享文本的函数
  const handleQuickShare = (code: {
    code: string;
    maxAccountCount: number;
    expiresAt: Date | null;
    product: {
      name: string;
      version: string;
    };
  }, id: string) => {
    const text = formatShareText(code);

    // 确保在客户端环境中执行
    if (typeof window !== 'undefined') {
      // 使用navigator.clipboard API复制
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
          .then(() => {
            setCopySuccess({id, success: true});
            // 2秒后清除成功状态
            setTimeout(() => setCopySuccess(null), 2000);
          })
          .catch(err => {
            console.error('复制失败:', err);
            setCopySuccess({id, success: false});
            setTimeout(() => setCopySuccess(null), 2000);
          });
      } else {
        // 回退方案，使用一个临时的textarea元素
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          const successful = document.execCommand('copy');
          setCopySuccess({id, success: successful});
          setTimeout(() => setCopySuccess(null), 2000);
        } catch (err) {
          console.error('复制失败:', err);
          setCopySuccess({id, success: false});
          setTimeout(() => setCopySuccess(null), 2000);
        }

        document.body.removeChild(textArea);
      }
    } else {
      // 在服务器端，只设置成功状态，实际复制会在客户端执行
      setCopySuccess({id, success: true});
      setTimeout(() => setCopySuccess(null), 2000);
    }
  };

  // 处理选择所有授权令牌
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedInviteCodes([]);
    } else {
      setSelectedInviteCodes(inviteCodes.map(code => code.id));
    }
    setSelectAll(!selectAll);
  };

  // 处理单个授权令牌的选择
  const handleSelectInviteCode = (id: string) => {
    setSelectedInviteCodes(prev => {
      if (prev.includes(id)) {
        return prev.filter(codeId => codeId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // 处理批量续期
  const handleBatchRenewal = (data: {
    newExpiry: Date;
    newCount?: number;
    fee: number;
    remark: string;
    productId?: string;
    renewalMode: string;
  }) => {
    const formData = new FormData();
    selectedInviteCodes.forEach(id => {
      formData.append("ids[]", id);
    });
    formData.append("newExpiry", data.newExpiry.toISOString());
    if (data.newCount !== undefined) {
      formData.append("newCount", data.newCount.toString());
    }
    formData.append("fee", data.fee.toString());
    formData.append("remark", data.remark);
    if (data.productId) {
      formData.append("productId", data.productId);
    }
    formData.append("renewalMode", data.renewalMode);
    formData.append("intent", "batchRenew");
    submit(formData, { method: "post" });
    setShowBatchRenewalDialog(false);
    setSelectedInviteCodes([]);
    setSelectAll(false);
  };

  // 处理显示账号列表
  const handleShowAccounts = (code: {
    code: string;
    tikTokAccounts: any[];
    maxAccountCount: number;
    deviceSessions: any[];
  }) => {
    // 提供完整数据，避免组件自动刷新
    setSelectedInviteCodeAccounts({
      code: code.code,
      accounts: code.tikTokAccounts || [],
      maxAccountCount: code.maxAccountCount,
      devices: code.deviceSessions || []
    });
    setShowAccountList(true);
  };

  // 处理菜单授权
  const handleMenuAuth = async (code: {
    id: string;
    code: string;
  }) => {
    try {
      // 查询授权令牌的菜单关联
      const response = await fetch(`/api/menus/${code.id}`);
      const inviteCodeMenus = await response.json();

      // 获取已授权的菜单ID列表
      const authorizedMenuIds = inviteCodeMenus.success
        ? inviteCodeMenus.data.map((menu: any) => menu.menuId)
        : [];

      setMenuAuthCode({
        id: code.id,
        code: code.code,
        authorizedMenuIds
      });
    } catch (error) {
      console.error("获取菜单授权信息失败:", error);
      // 使用空数组作为默认值
      setMenuAuthCode({
        id: code.id,
        code: code.code,
        authorizedMenuIds: []
      });
    }
  };

  // 在 Pagination 组件使用处添加计算逻辑
  const paginationStart = pagination.total > 0
    ? (pagination.page - 1) * pagination.pageSize + 1
    : 0;
  const paginationEnd = Math.min(pagination.page * pagination.pageSize, pagination.total);

  return (
    <AdminLayout user={{ name: user?.username || "管理员", role: user?.role }}>
      <div className="min-h-screen bg-gray-50 py-4 sm:py-10">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <div className="max-w-[2000px] mx-auto px-2 sm:px-6 lg:px-8">
            {/* 页面头部 */}
            <PageHeader totalIncome={totalIncome} />

            {/* 临时连接管理链接 */}
            <div className="mb-4 p-4 bg-yellow-100 rounded-lg">
              <p className="text-yellow-800 mb-2">如果连接管理菜单无法正常工作，请尝试直接点击下面的链接：</p>
              <a
                href="/connections"
                className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                onClick={(e) => {
                  if (typeof window !== 'undefined') {
                    window.location.href = '/connections';
                  }
                }}
              >
                直接访问连接管理页面
              </a>
            </div>

            {/* 搜索和过滤 */}
            <SearchAndFilter
              activeTab={activeTab}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              searchType={searchType}
              setSearchType={setSearchType}
              autoRefresh={autoRefresh}
              setAutoRefresh={setAutoRefresh}
              refreshInterval={refreshInterval}
              setRefreshInterval={setRefreshInterval}
              selectedInviteCodes={selectedInviteCodes}
              onOpenCreateModal={() => setIsModalOpen(true)}
              onOpenBatchRenewalDialog={() => setShowBatchRenewalDialog(true)}
            />

            {/* 标签页导航 */}
            <TabNavigation
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              totalCount={pagination.total}
            />

            {/* 列选择器 */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
              <div className="mb-2 sm:mb-0">
                <ColumnSelector
                  columns={defaultColumns}
                  visibleColumns={visibleColumns}
                  onColumnToggle={handleColumnToggle}
                />
              </div>
            </div>

            {/* 响应式表格容器 */}
            <div className="bg-white/30 backdrop-blur-md rounded-xl shadow-sm border border-gray-200/50">
              <div className="border-b border-gray-200">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200/50">
                  <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium text-gray-900">
                      {activeTab === 'active' ? '授权令牌列表' : '已删除的授权令牌'}
                    </h2>
                    <span className="text-sm text-gray-500">
                      共 {pagination.total} 条记录
                    </span>
                  </div>
                </div>

                {/* 授权令牌表格 */}
                <InviteCodeTable
                  inviteCodes={inviteCodes}
                  activeTab={activeTab}
                  visibleColumns={visibleColumns}
                  selectedInviteCodes={selectedInviteCodes}
                  onSelectInviteCode={handleSelectInviteCode}
                  onSelectAll={handleSelectAll}
                  selectAll={selectAll}
                  onEdit={setEditingCode}
                  onDelete={setDeleteId}
                  onClearDevice={setClearingDeviceId}
                  onShare={handleShare}
                  onQuickShare={handleQuickShare}
                  onPayment={setPayingCode}
                  onMenuAuth={handleMenuAuth}
                  onRestore={handleRestore}
                  onPermanentDelete={handlePermanentDelete}
                  onTogglePaid={setTogglePaidId}
                  onShowAccounts={handleShowAccounts}
                  copySuccess={copySuccess}
                  defaultColumns={defaultColumns}
                />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{paginationStart}</span> 到{" "}
                  <span className="font-medium">{paginationEnd}</span> 条，共{" "}
                  <span className="font-medium">{pagination.total}</span> 条记录
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 各种对话框 */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="创建新授权令牌"
        maxWidth="max-w-4xl"
        fullScreenOnMobile={true}
      >
        <CreateInviteCodeForm
          onSubmit={() => {
            // 不需要阻止默认行为，让表单正常提交
            // 表单提交后会自动关闭模态框
            setTimeout(() => {
              setIsModalOpen(false);
            }, 100);
          }}
          isSubmitting={isSubmitting}
          onClose={() => setIsModalOpen(false)}
          products={products}
        />
      </Modal>

      <ConfirmDialog
        open={deleteId !== null}
        title="删除授权令牌"
        content="确定要删除这个授权令牌吗？此操作无法撤销。"
        onConfirm={() => deleteId && handleDelete(deleteId)}
        onCancel={() => setDeleteId(null)}
      />

      <EditDialog
        open={editingCode !== null}
        onClose={() => setEditingCode(null)}
        onConfirm={handleEdit}
        initialData={editingCode || {
          id: '',
          wechatId: '',
          remark: '',
          maxAccountCount: 1,
          expiresAt: null,
          productId: '',
          enforceAccountLimit: true,
        }}
        products={products}
      />

      <RenewalDialog
        open={renewingCode !== null}
        onClose={() => setRenewingCode(null)}
        onConfirm={handleRenewal}
        initialData={
          renewingCode
            ? {
                currentExpiry: renewingCode.expiresAt,
                currentCount: renewingCode.maxAccountCount,
                productId: renewingCode.productId,
              }
            : { currentExpiry: null, currentCount: 1, productId: '' }
        }
      />

      <ConfirmDialog
        open={clearingDeviceId !== null}
        title="清除设备"
        content="确定要清除该授权令牌的设备登录状态吗？此操作将导致用户需要重新登录。"
        onConfirm={() => clearingDeviceId && handleClearDevice(clearingDeviceId)}
        onCancel={() => setClearingDeviceId(null)}
      />

      <ShareDialog
        open={shareDialogOpen}
        onClose={() => setShareDialogOpen(false)}
        shareText={shareText}
        inviteCode={currentShareCode}
      />

      <PaymentDialog
        open={payingCode !== null}
        onClose={() => setPayingCode(null)}
        inviteCode={payingCode}
      />

      <ConfirmDialog
        open={togglePaidId !== null}
        title="修改付费状态"
        content="确定要修改此授权令牌的付费状态吗？"
        onConfirm={() => togglePaidId && handleTogglePaid(togglePaidId)}
        onCancel={() => setTogglePaidId(null)}
      />

      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
        />
      )}

      {showError && actionData?.error && (
        <ConfirmDialog
          open={showError}
          title="操作失败"
          message={actionData.error}
          confirmText="确定"
          onConfirm={() => setShowError(false)}
          onCancel={() => setShowError(false)}
        />
      )}

      <BatchRenewalDialog
        open={showBatchRenewalDialog}
        onClose={() => setShowBatchRenewalDialog(false)}
        onConfirm={handleBatchRenewal}
        selectedCount={selectedInviteCodes.length}
        products={products}
      />

      <AccountListDialog
        open={showAccountList}
        onClose={() => setShowAccountList(false)}
        data={selectedInviteCodeAccounts}
      />

      {/* 菜单授权对话框 */}
      {menuAuthCode && (
        <MenuAuthDialog
          isOpen={menuAuthCode !== null}
          open={menuAuthCode !== null}
          onClose={() => setMenuAuthCode(null)}
          inviteCodeId={menuAuthCode.id}
          inviteCodeName={menuAuthCode.code}
          allMenus={systemMenus}
          authorizedMenuIds={menuAuthCode.authorizedMenuIds}
        />
      )}
    </AdminLayout>
  );
}
