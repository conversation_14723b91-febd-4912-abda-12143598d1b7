import type { MetaFunction } from "@remix-run/node";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import AdminLayout from "../components/Layout";
import { db } from "~/utils/db.server";
import { requireUserId, getUser } from "~/utils/session.server";
import { UsersIcon, ClockIcon, CurrencyYenIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import StatCard from '~/components/StatCard';
import LineChart from '~/components/LineChart';
import { motion } from "framer-motion";

export const meta: MetaFunction = () => {
  return [{ title: "控制台 | 管理后台" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  // 获取总收入（代理商只看自己创建的授权令牌的收入）
  let totalIncomeResult;
  if (user?.role === 'AGENT') {
    totalIncomeResult = await db.$queryRaw`
      SELECT COALESCE(SUM(pr.amount), 0) as total
      FROM "PaymentRecord" pr
      JOIN "InviteCode" ic ON pr."inviteCodeId" = ic.id
      WHERE ic."createdBy" = ${user.id}
    `;
  } else {
    totalIncomeResult = await db.paymentRecord.aggregate({
      _sum: { amount: true }
    });
  }

  // 获取今日收入
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  let todayIncomeResult;
  if (user?.role === 'AGENT') {
    todayIncomeResult = await db.$queryRaw`
      SELECT COALESCE(SUM(pr.amount), 0) as total
      FROM "PaymentRecord" pr
      JOIN "InviteCode" ic ON pr."inviteCodeId" = ic.id
      WHERE ic."createdBy" = ${user.id} AND pr."paidAt" >= ${today}
    `;
  } else {
    todayIncomeResult = await db.paymentRecord.aggregate({
      where: {
        paidAt: { gte: today }
      },
      _sum: { amount: true }
    });
  }

  // 获取总用户数和付费用户数（代理商只看自己创建的授权令牌）
  let totalUsers, paidUsers;
  if (user?.role === 'AGENT') {
    const userCountResult = await db.$queryRaw`
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN "isPaid" = true THEN 1 END) as paid_users
      FROM "InviteCode"
      WHERE "deletedAt" IS NULL AND "createdBy" = ${user.id}
    ` as any[];

    totalUsers = Number(userCountResult[0]?.total_users || 0);
    paidUsers = Number(userCountResult[0]?.paid_users || 0);
  } else {
    totalUsers = await db.inviteCode.count({
      where: { deletedAt: null }
    });

    paidUsers = await db.inviteCode.count({
      where: {
        deletedAt: null,
        isPaid: true
      }
    });
  }

  // 获取在线设备数（代理商只看自己创建的授权令牌的设备）
  let onlineDevices;
  if (user?.role === 'AGENT') {
    const deviceCountResult = await db.$queryRaw`
      SELECT COUNT(*) as count
      FROM "DeviceSession" ds
      JOIN "InviteCode" ic ON ds."inviteCodeId" = ic.id
      WHERE ic."createdBy" = ${user.id}
        AND ds."lastActiveAt" >= ${new Date(Date.now() - 30 * 60 * 1000)}
    ` as any[];
    onlineDevices = Number(deviceCountResult[0]?.count || 0);
  } else {
    onlineDevices = await db.deviceSession.count({
      where: {
        lastActiveAt: {
          gte: new Date(Date.now() - 30 * 60 * 1000) // 30分钟内活跃
        }
      }
    });
  }

  // 获取各产品的统计数据（代理商只看自己创建的授权令牌）
  let productStats;
  if (user?.role === 'AGENT') {
    const productStatsResult = await db.$queryRaw`
      SELECT
        p.id,
        p.name,
        p.version,
        COUNT(ic.id) as total_users,
        COUNT(CASE WHEN ic."isPaid" = true THEN 1 END) as paid_users
      FROM "Product" p
      LEFT JOIN "InviteCode" ic ON p.id = ic."productId"
        AND ic."deletedAt" IS NULL
        AND ic."createdBy" = ${user.id}
      WHERE p."isEnabled" = true
      GROUP BY p.id, p.name, p.version
    ` as any[];

    productStats = productStatsResult.map((product: any) => ({
      id: product.id,
      name: product.name,
      version: product.version,
      totalUsers: Number(product.total_users || 0),
      paidUsers: Number(product.paid_users || 0),
      paidRate: Number(product.total_users || 0) > 0
        ? (Number(product.paid_users || 0) / Number(product.total_users || 0) * 100).toFixed(1)
        : '0'
    }));
  } else {
    const products = await db.product.findMany({
      where: { isEnabled: true },
      select: {
        id: true,
        name: true,
        version: true,
        _count: {
          select: {
            inviteCodes: {
              where: {
                deletedAt: null
              }
            }
          }
        }
      }
    });

    // 获取每个产品的付费用户数
    productStats = await Promise.all(
      products.map(async (product) => {
        const paidUsers = await db.inviteCode.count({
          where: {
            productId: product.id,
            isPaid: true,
            deletedAt: null
          }
        });

        return {
          ...product,
          paidUsers,
          totalUsers: product._count.inviteCodes,
          paidRate: product._count.inviteCodes > 0
            ? (paidUsers / product._count.inviteCodes * 100).toFixed(1)
            : 0
        };
      })
    );
  }

  // 获取最近7天的收入趋势（代理商只看自己创建的授权令牌）
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  let dailyIncome;
  if (user?.role === 'AGENT') {
    const dailyIncomeResult = await db.$queryRaw`
      SELECT
        DATE(pr."paidAt") as paid_date,
        SUM(pr.amount) as total_amount
      FROM "PaymentRecord" pr
      JOIN "InviteCode" ic ON pr."inviteCodeId" = ic.id
      WHERE ic."createdBy" = ${user.id} AND pr."paidAt" >= ${sevenDaysAgo}
      GROUP BY DATE(pr."paidAt")
      ORDER BY paid_date
    ` as any[];

    dailyIncome = dailyIncomeResult.map((day: any) => ({
      paidAt: day.paid_date,
      _sum: { amount: Number(day.total_amount || 0) }
    }));
  } else {
    dailyIncome = await db.paymentRecord.groupBy({
      by: ['paidAt'],
      where: {
        paidAt: {
          gte: sevenDaysAgo
        }
      },
      _sum: {
        amount: true
      }
    });
  }

  // 处理收入数据
  const totalIncomeAmount = user?.role === 'AGENT'
    ? Number((totalIncomeResult as any[])[0]?.total || 0)
    : Number((totalIncomeResult as any)?._sum?.amount || 0);

  const todayIncomeAmount = user?.role === 'AGENT'
    ? Number((todayIncomeResult as any[])[0]?.total || 0)
    : Number((todayIncomeResult as any)?._sum?.amount || 0);

  return json({
    user,
    totalIncome: totalIncomeAmount,
    todayIncome: todayIncomeAmount,
    totalUsers,
    paidUsers,
    paidRate: totalUsers > 0 ? (paidUsers / totalUsers * 100).toFixed(1) : '0',
    onlineDevices,
    productStats,
    dailyIncome: dailyIncome.map(day => ({
      date: new Date(day.paidAt).toISOString(),
      amount: Number(day._sum.amount || 0)
    }))
  });
}

export default function Dashboard() {
  const { 
    user, 
    totalIncome, 
    todayIncome,
    totalUsers,
    paidUsers,
    paidRate,
    onlineDevices,
    productStats, 
    dailyIncome 
  } = useLoaderData<typeof loader>();

  const chartData = {
    labels: dailyIncome.map(d => new Date(d.date).toLocaleDateString('zh-CN')),
    datasets: [
      {
        label: '收入',
        data: dailyIncome.map(d => d.amount),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
      },
    ],
  };

  return (
    <AdminLayout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">控制台</h1>
              <p className="mt-1 text-sm text-gray-500">
                欢迎回来，这里是您的数据概览
              </p>
            </div>
          </div>

          {/* 总览数据卡片 */}
          <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyYenIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">总收入</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          ¥{totalIncome.toFixed(2)}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyYenIcon className="h-6 w-6 text-green-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">今日收入</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          ¥{todayIncome.toFixed(2)}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersIcon className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">用户数据</dt>
                      <dd className="mt-1">
                        <div className="text-2xl font-semibold text-gray-900">{totalUsers}</div>
                        <div className="text-sm text-gray-500">
                          付费用户：{paidUsers} ({paidRate}%)
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ClockIcon className="h-6 w-6 text-indigo-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">在线设备</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {onlineDevices}
                        </div>
                        <span className="ml-2 text-sm text-gray-500">台</span>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 产品数据 */}
          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-900">产品数据</h2>
            <div className="mt-4 flex flex-col">
              <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                  <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            产品名称
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            版本
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            总用户数
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            付费用户数
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            付费率
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {productStats.map((product) => (
                          <tr key={product.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {product.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {product.version}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {product.totalUsers}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {product.paidUsers}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {product.paidRate}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 收入趋势图表 */}
          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-900">收入趋势</h2>
            <div className="mt-4 bg-white p-6 rounded-lg shadow">
              <LineChart
                title="收入趋势"
                data={chartData}
              />
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
} 