import { useState, useEffect } from "react";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, Link, useSubmit, useActionData } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { db as prisma } from "~/utils/db.server";
import {
  TicketIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ChatBubbleLeftIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  UserIcon
} from "@heroicons/react/24/outline";

export async function action({ request }: ActionFunctionArgs) {
  await requireUserId(request);

  const formData = await request.formData();
  const actionType = formData.get("action") as string;

  try {
    if (actionType === "createTicket") {
      const title = formData.get("title") as string;
      const content = formData.get("content") as string;
      const customerName = formData.get("customerName") as string;
      const customerEmail = formData.get("customerEmail") as string;
      const customerPhone = formData.get("customerPhone") as string;
      const priority = formData.get("priority") as string;
      const inviteCodeId = formData.get("inviteCodeId") as string;

      // 验证必填字段
      if (!title || !content) {
        return json(
          { success: false, error: "标题和内容为必填项" },
          { status: 400 }
        );
      }

      const ticket = await prisma.ticket.create({
        data: {
          title,
          content,
          customerName: customerName || null,
          customerEmail: customerEmail || null,
          customerPhone: customerPhone || null,
          priority: priority || 'MEDIUM',
          inviteCodeId: inviteCodeId || null
        }
      });

      return json({ success: true, ticket });
    } else if (actionType === "addReply") {
      const ticketId = formData.get("ticketId") as string;
      const content = formData.get("content") as string;
      const authorName = formData.get("authorName") as string;

      if (!content.trim()) {
        return json(
          { success: false, error: "回复内容不能为空" },
          { status: 400 }
        );
      }

      // 创建回复
      await prisma.ticketReply.create({
        data: {
          ticketId,
          content,
          isAdmin: true,
          authorName: authorName || "管理员"
        }
      });

      // 更新工单状态为处理中
      await prisma.ticket.update({
        where: { id: ticketId },
        data: {
          status: 'IN_PROGRESS',
          updatedAt: new Date()
        }
      });

      return json({ success: true });
    }

    return json({ success: false, error: "无效的操作" }, { status: 400 });
  } catch (error) {
    console.error("操作失败:", error);
    return json({ success: false, error: "操作失败" }, { status: 500 });
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  // 只有管理员可以访问工单管理页面
  if (user?.role !== 'ADMIN') {
    throw new Response("Forbidden", { status: 403 });
  }

  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "10");
  const status = url.searchParams.get("status");
  const priority = url.searchParams.get("priority");
  const search = url.searchParams.get("search");
  const inviteCodeId = url.searchParams.get("inviteCodeId");

  const skip = (page - 1) * limit;

  // 构建查询条件
  const where: any = {};

  if (status) {
    where.status = status;
  }

  if (priority) {
    where.priority = priority;
  }

  if (inviteCodeId) {
    where.inviteCodeId = inviteCodeId;
  }

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
      { customerName: { contains: search, mode: 'insensitive' } },
      { customerEmail: { contains: search, mode: 'insensitive' } }
    ];
  }

  const [tickets, total, inviteCodes] = await Promise.all([
    prisma.ticket.findMany({
      where,
      include: {
        replies: {
          orderBy: { createdAt: 'desc' },
          take: 1
        },
        inviteCode: {
          select: {
            id: true,
            code: true,
            remark: true,
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.ticket.count({ where }),
    prisma.inviteCode.findMany({
      where: {
        isEnabled: true,
        deletedAt: null
      },
      include: {
        product: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  ]);

  return json({
    tickets,
    inviteCodes,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    },
    filters: { status, priority, search, inviteCodeId }
  });
}

export default function TicketsPage() {
  const { tickets, inviteCodes, pagination, filters } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState(filters.status || "");
  const [priorityFilter, setPriorityFilter] = useState(filters.priority || "");
  const [inviteCodeFilter, setInviteCodeFilter] = useState(filters.inviteCodeId || "");

  // 新建工单弹窗状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [createFormData, setCreateFormData] = useState({
    title: "",
    content: "",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    priority: "MEDIUM",
    inviteCodeId: ""
  });

  // 回复弹窗状态
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [replyTicketId, setReplyTicketId] = useState("");
  const [replyContent, setReplyContent] = useState("");
  const [authorName, setAuthorName] = useState("管理员");

  const statusOptions = [
    { value: "", label: "全部状态" },
    { value: "OPEN", label: "待处理" },
    { value: "IN_PROGRESS", label: "处理中" },
    { value: "RESOLVED", label: "已解决" },
    { value: "CLOSED", label: "已关闭" }
  ];

  const priorityOptions = [
    { value: "", label: "全部优先级" },
    { value: "LOW", label: "低" },
    { value: "MEDIUM", label: "中" },
    { value: "HIGH", label: "高" },
    { value: "URGENT", label: "紧急" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-red-100 text-red-800";
      case "IN_PROGRESS":
        return "bg-yellow-100 text-yellow-800";
      case "RESOLVED":
        return "bg-green-100 text-green-800";
      case "CLOSED":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "LOW":
        return "bg-blue-100 text-blue-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "HIGH":
        return "bg-orange-100 text-orange-800";
      case "URGENT":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleSearch = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    if (statusFilter) params.set("status", statusFilter);
    if (priorityFilter) params.set("priority", priorityFilter);
    if (inviteCodeFilter) params.set("inviteCodeId", inviteCodeFilter);

    window.location.href = `/tickets?${params.toString()}`;
  };

  const handleCreateTicket = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("action", "createTicket");
    Object.entries(createFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    submit(formData, { method: "post" });
  };

  const handleReply = (e: React.FormEvent) => {
    e.preventDefault();

    if (!replyContent.trim()) return;

    const formData = new FormData();
    formData.append("action", "addReply");
    formData.append("ticketId", replyTicketId);
    formData.append("content", replyContent);
    formData.append("authorName", authorName);

    submit(formData, { method: "post" });
  };

  const openReplyModal = (ticketId: string) => {
    setReplyTicketId(ticketId);
    setReplyContent("");
    setShowReplyModal(true);
  };

  // 获取当前回复工单的详细信息
  const currentTicket = tickets.find(t => t.id === replyTicketId);

  // 处理成功响应
  useEffect(() => {
    if (actionData?.success) {
      if (actionData.ticket) {
        // 新建工单成功
        setShowCreateModal(false);
        setCreateFormData({
          title: "",
          content: "",
          customerName: "",
          customerEmail: "",
          customerPhone: "",
          priority: "MEDIUM",
          inviteCodeId: ""
        });
        window.location.reload();
      } else {
        // 回复成功
        setShowReplyModal(false);
        setReplyContent("");
        window.location.reload();
      }
    }
  }, [actionData]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <TicketIcon className="h-8 w-8 text-gray-600 mr-3" />
            <h1 className="text-2xl font-bold text-gray-900">工单管理</h1>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            新建工单
          </button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜索
            </label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索标题、内容、客户信息..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              优先级
            </label>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              {priorityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              授权令牌
            </label>
            <select
              value={inviteCodeFilter}
              onChange={(e) => setInviteCodeFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">全部令牌</option>
              {inviteCodes.map((inviteCode) => (
                <option key={inviteCode.id} value={inviteCode.id}>
                  {inviteCode.code} - {inviteCode.product.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={handleSearch}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              筛选
            </button>
          </div>
        </div>
      </div>

      {/* 工单列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {tickets.map((ticket) => (
            <li key={ticket.id}>
              <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <Link to={`/tickets/${ticket.id}`} className="block">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          {ticket.title}
                        </p>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                            {statusOptions.find(s => s.value === ticket.status)?.label}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                            {priorityOptions.find(p => p.value === ticket.priority)?.label}
                          </span>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {ticket.content}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500">
                          <span>
                            {ticket.customerName && `${ticket.customerName} • `}
                            {ticket.customerEmail && `${ticket.customerEmail} • `}
                            创建于 {formatDate(ticket.createdAt)}
                          </span>
                          {ticket.replies.length > 0 && (
                            <span className="ml-4">
                              • {ticket.replies.length} 条回复
                            </span>
                          )}
                        </div>
                        {ticket.inviteCode && (
                          <div className="flex items-center">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                              {ticket.inviteCode.code}
                              {ticket.inviteCode.product && ` - ${ticket.inviteCode.product.name}`}
                            </span>
                          </div>
                        )}
                      </div>
                    </Link>
                  </div>
                  <div className="ml-4 flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        openReplyModal(ticket.id);
                      }}
                      className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <ChatBubbleLeftIcon className="h-3 w-3 mr-1" />
                      回复
                    </button>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            显示第 {(pagination.page - 1) * pagination.limit + 1} 到{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
            共 {pagination.total} 条记录
          </div>
          <div className="flex space-x-2">
            {pagination.page > 1 && (
              <Link
                to={`/tickets?page=${pagination.page - 1}&${new URLSearchParams(filters).toString()}`}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                上一页
              </Link>
            )}
            {pagination.page < pagination.totalPages && (
              <Link
                to={`/tickets?page=${pagination.page + 1}&${new URLSearchParams(filters).toString()}`}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                下一页
              </Link>
            )}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {tickets.length === 0 && (
        <div className="text-center py-12">
          <TicketIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无工单</h3>
          <p className="mt-1 text-sm text-gray-500">
            {Object.values(filters).some(f => f) ? "没有找到符合条件的工单" : "还没有创建任何工单"}
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              创建第一个工单
            </button>
          </div>
        </div>
      )}

      {/* 新建工单弹窗 */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCreateModal(false)}></div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleCreateTicket}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">新建工单</h3>
                    <button
                      type="button"
                      onClick={() => setShowCreateModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>

                  {actionData?.error && (
                    <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <p className="text-sm text-red-700">{actionData.error}</p>
                    </div>
                  )}

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        工单标题 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={createFormData.title}
                        onChange={(e) => setCreateFormData(prev => ({ ...prev, title: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="请输入工单标题"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        问题描述 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={createFormData.content}
                        onChange={(e) => setCreateFormData(prev => ({ ...prev, content: e.target.value }))}
                        rows={4}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="请详细描述遇到的问题..."
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          客户姓名
                        </label>
                        <input
                          type="text"
                          value={createFormData.customerName}
                          onChange={(e) => setCreateFormData(prev => ({ ...prev, customerName: e.target.value }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="客户姓名"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          客户邮箱
                        </label>
                        <input
                          type="email"
                          value={createFormData.customerEmail}
                          onChange={(e) => setCreateFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="客户邮箱"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          优先级
                        </label>
                        <select
                          value={createFormData.priority}
                          onChange={(e) => setCreateFormData(prev => ({ ...prev, priority: e.target.value }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          {priorityOptions.slice(1).map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          关联授权令牌
                        </label>
                        <select
                          value={createFormData.inviteCodeId}
                          onChange={(e) => setCreateFormData(prev => ({ ...prev, inviteCodeId: e.target.value }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="">请选择（可选）</option>
                          {inviteCodes.map((inviteCode) => (
                            <option key={inviteCode.id} value={inviteCode.id}>
                              {inviteCode.code} - {inviteCode.product.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    disabled={!createFormData.title || !createFormData.content}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    创建工单
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    取消
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* 回复弹窗 */}
      {showReplyModal && currentTicket && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowReplyModal(false)}></div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <form onSubmit={handleReply}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">回复工单：{currentTicket.title}</h3>
                    <button
                      type="button"
                      onClick={() => setShowReplyModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>

                  {actionData?.error && (
                    <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <p className="text-sm text-red-700">{actionData.error}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 左侧：沟通历史 */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">沟通历史</h4>
                      <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                        {/* 原始工单内容 */}
                        <div className="mb-4 pb-4 border-b border-gray-200">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <UserIcon className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <p className="text-sm font-medium text-gray-900">
                                  {currentTicket.customerName || "客户"}
                                </p>
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                  工单创建
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatDate(currentTicket.createdAt)}
                              </p>
                              <div className="mt-2">
                                <p className="text-sm text-gray-700 whitespace-pre-wrap">
                                  {currentTicket.content}
                                </p>
                              </div>
                              {currentTicket.customerEmail && (
                                <p className="text-xs text-gray-500 mt-1">
                                  邮箱：{currentTicket.customerEmail}
                                </p>
                              )}
                              {currentTicket.inviteCode && (
                                <div className="mt-1">
                                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                                    {currentTicket.inviteCode.code}
                                    {currentTicket.inviteCode.product && ` - ${currentTicket.inviteCode.product.name}`}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* 回复历史 */}
                        {currentTicket.replies && currentTicket.replies.length > 0 ? (
                          <div className="space-y-4">
                            {currentTicket.replies.map((reply) => (
                              <div key={reply.id} className="flex items-start space-x-3">
                                <div className="flex-shrink-0">
                                  <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                                    reply.isAdmin
                                      ? 'bg-green-100'
                                      : 'bg-blue-100'
                                  }`}>
                                    {reply.isAdmin ? (
                                      <ChatBubbleLeftIcon className="h-4 w-4 text-green-600" />
                                    ) : (
                                      <UserIcon className="h-4 w-4 text-blue-600" />
                                    )}
                                  </div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center space-x-2">
                                    <p className="text-sm font-medium text-gray-900">
                                      {reply.authorName}
                                    </p>
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                      reply.isAdmin
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-blue-100 text-blue-800'
                                    }`}>
                                      {reply.isAdmin ? '管理员回复' : '客户回复'}
                                    </span>
                                  </div>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {formatDate(reply.createdAt)}
                                  </p>
                                  <div className="mt-2">
                                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                                      {reply.content}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500 text-center py-4">
                            暂无回复记录
                          </p>
                        )}
                      </div>
                    </div>

                    {/* 右侧：添加回复 */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">添加新回复</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            回复者姓名
                          </label>
                          <input
                            type="text"
                            value={authorName}
                            onChange={(e) => setAuthorName(e.target.value)}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="请输入回复者姓名"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            回复内容 <span className="text-red-500">*</span>
                          </label>
                          <textarea
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            rows={8}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="请输入回复内容..."
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    disabled={!replyContent.trim()}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                    发送回复
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowReplyModal(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    取消
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
