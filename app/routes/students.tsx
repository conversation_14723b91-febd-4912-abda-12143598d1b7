import type { MetaFunction } from "@remix-run/node";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigation, Form, useSubmit, useSearchParams, useActionData } from "@remix-run/react";
import { useState, useEffect, useRef } from "react";
import crypto from "crypto";
import { db } from "~/utils/db.server";
import AdminLayout from "~/components/Layout";
import Modal from "~/components/Modal";
import { requireUserId, getUser } from "~/utils/session.server";
import ConfirmDialog from "~/components/ConfirmDialog";
import { MagnifyingGlassIcon, PlusIcon, CurrencyYenIcon, EllipsisHorizontalIcon, CreditCardIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { motion } from "framer-motion";
import Pagination from "~/components/Pagination";
import Toast from "~/components/Toast";
import ColumnSelector from "~/components/ColumnSelector";

export const meta: MetaFunction = () => {
  return [
    { title: "学员管理 - 后台管理系统" },
    { name: "description", content: "学员管理页面" },
  ];
};

// 帮助函数：安全地将任何值转换为JSON安全类型
function toJsonSafe(value: any): any {
  if (value === null || value === undefined) {
    return value;
  }
  if (typeof value === 'bigint') {
    return Number(value);
  }
  if (value instanceof Date) {
    return value.toISOString();
  }
  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return value.map(toJsonSafe);
    }
    const result: any = {};
    for (const key in value) {
      result[key] = toJsonSafe(value[key]);
    }
    return result;
  }
  // 如果是String、Number、Boolean等原始类型，直接返回
  return value;
}

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  // 只有管理员可以访问学员管理页面
  if (user?.role !== 'ADMIN') {
    throw new Response("Forbidden", { status: 403 });
  }
  
  const url = new URL(request.url);
  const searchParams = new URLSearchParams(url.search);
  const page = parseInt(searchParams.get("page") || "1", 10);
  const pageSize = parseInt(searchParams.get("pageSize") || "10", 10);
  const skip = (page - 1) * pageSize;
  const search = searchParams.get("search") || "";
  const searchType = searchParams.get("searchType") || "name";

  // 构建搜索条件
  const where = {
    ...(search ? {
      [searchType]: {
        contains: search,
        mode: 'insensitive',
      }
    } : {}),
  };

  // 查询学员列表
  const studentsData = await db.$queryRaw`
    SELECT s.*, 
      (SELECT COUNT(*) FROM "PaymentStudent" ps WHERE ps."studentId" = s.id) as payment_count,
      (SELECT COUNT(*) FROM "TopupRecord" tr WHERE tr."studentId" = s.id) as topup_count
    FROM "Student" s
    ORDER BY s."createdAt" DESC
    LIMIT ${pageSize} OFFSET ${skip}
  `;
  
  const students = (studentsData as any[]).map(s => ({
    ...s,
    id: s.id,
    name: s.name,
    wechat: s.wechat,
    phone: s.phone,
    email: s.email,
    storeCount: typeof s.storeCount === 'bigint' ? Number(s.storeCount) : Number(s.storeCount || 0),
    balance: typeof s.balance === 'bigint' ? Number(s.balance) : Number(s.balance || 0),
    createdAt: s.createdAt instanceof Date ? s.createdAt.toISOString() : s.createdAt,
    updatedAt: s.updatedAt instanceof Date ? s.updatedAt.toISOString() : s.updatedAt,
    _count: {
      payments: parseInt(String(s.payment_count)),
      topups: parseInt(String(s.topup_count))
    }
  }));

  // 计算总学员数
  const totalResult = await db.$queryRaw`SELECT COUNT(*) as count FROM "Student"`;
  const total = parseInt(String(((totalResult as any[])[0]?.count === null ? '0' : (totalResult as any[])[0]?.count)));

  // 计算总货款和学费金额
  const paymentStatsData = await db.$queryRaw`
    SELECT type, SUM(amount) as total
    FROM "PaymentStudent"
    GROUP BY type
  `;
  
  const paymentStats = (paymentStatsData as any[])?.map(stat => ({
    type: stat.type,
    _sum: { amount: stat.total === null ? 0 : Number(String(stat.total)) }
  })) || [];

  // 提取不同类型的付款总额
  const goodsTotal = paymentStats.find(item => item.type === 'GOODS')?._sum?.amount || 0;
  const tuitionTotal = paymentStats.find(item => item.type === 'TUITION')?._sum?.amount || 0;
  const otherTotal = paymentStats.find(item => item.type === 'OTHER')?._sum?.amount || 0;

  // 计算总充值金额
  const topupTotalData = await db.$queryRaw`SELECT SUM(amount) as total FROM "TopupRecord"`;
  const topupTotal = { 
    _sum: { 
      amount: ((topupTotalData as any[])[0]?.total === null ? 0 : Number(String((topupTotalData as any[])[0]?.total))) || 0 
    } 
  };

  // 计算所有学员当前余额总和
  const balanceTotalData = await db.$queryRaw`SELECT SUM(balance) as total FROM "Student"`;
  const balanceTotal = { 
    _sum: { 
      balance: ((balanceTotalData as any[])[0]?.total === null ? 0 : Number(String((balanceTotalData as any[])[0]?.total))) || 0 
    } 
  };

  // 确保分页数据都是有效的数字
  const totalPages = Math.ceil(total / pageSize) || 1; // 防止除以零或结果为零

  return json(toJsonSafe({
    students,
    currentUser: user,
    pagination: {
      total,
      page,
      pageSize,
      totalPages
    },
    stats: {
      goodsTotal: Number(goodsTotal) || 0,
      tuitionTotal: Number(tuitionTotal) || 0,
      otherTotal: Number(otherTotal) || 0,
      topupTotal: Number(topupTotal._sum.amount) || 0,
      balanceTotal: Number(balanceTotal._sum.balance) || 0
    }
  }));
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  await requireUserId(request);

  try {
    switch (intent) {
      case "create": {
        const name = formData.get("name") as string;
        const phone = formData.get("phone") as string;
        const email = formData.get("email") as string;
        const wechat = formData.get("wechat") as string;
        const storeCount = parseInt(formData.get("storeCount") as string || "0", 10);
        const remark = formData.get("remark") as string;
        const balance = parseFloat(formData.get("balance") as string || "0");
        
        if (!name || name.trim() === '') {
          return json(toJsonSafe({ error: "学员姓名不能为空" }), { status: 400 });
        }
        
        if (!wechat || wechat.trim() === '') {
          return json(toJsonSafe({ error: "微信号不能为空" }), { status: 400 });
        }

        // 创建学员
        try {
          // 使用原生SQL语句创建学员，避免Prisma客户端的潜在问题
          const uuid = crypto.randomUUID();
          
          await db.$executeRaw`
            INSERT INTO "Student" (
              "id", 
              "name", 
              "phone", 
              "email", 
              "wechat", 
              "storeCount", 
              "remark", 
              "balance", 
              "createdAt", 
              "updatedAt"
            ) VALUES (
              ${uuid}, 
              ${name}, 
              ${phone || null}, 
              ${email || null}, 
              ${wechat}, 
              ${storeCount}, 
              ${remark || null}, 
              ${balance}::numeric, 
              CURRENT_TIMESTAMP, 
              CURRENT_TIMESTAMP
            )
          `;
          
          return json(toJsonSafe({ success: true, message: "学员创建成功", studentId: uuid }));
        } catch (error) {
          console.error("创建学员失败:", error);
          return json(toJsonSafe({ error: "创建学员失败，请重试" }), { status: 500 });
        }
      }

      case "update": {
        const studentId = formData.get("studentId") as string;
        const name = formData.get("name") as string;
        const phone = formData.get("phone") as string;
        const email = formData.get("email") as string;
        const wechat = formData.get("wechat") as string;
        const storeCount = parseInt(formData.get("storeCount") as string || "0", 10);
        const remark = formData.get("remark") as string;
        
        if (!name || name.trim() === '') {
          return json(toJsonSafe({ error: "学员姓名不能为空" }), { status: 400 });
        }
        
        if (!wechat || wechat.trim() === '') {
          return json(toJsonSafe({ error: "微信号不能为空" }), { status: 400 });
        }
        
        // 检查学员是否存在
        const existingStudent = await db.student.findUnique({
          where: { id: studentId },
        });
        
        if (!existingStudent) {
          return json(toJsonSafe({ error: "学员不存在" }), { status: 404 });
        }
        
        // 更新学员
        await db.student.update({
          where: { id: studentId },
          data: {
            name,
            phone: phone || null,
            email: email || null,
            wechat,
            storeCount,
            remark: remark || null,
          },
        });
        
        return json(toJsonSafe({ success: true, message: "学员更新成功" }));
      }

      case "delete": {
        const studentId = formData.get("studentId") as string;
        
        // 检查是否存在
        const student = await db.student.findUnique({
          where: { id: studentId },
        });
        
        if (!student) {
          return json(toJsonSafe({ error: "学员不存在" }), { status: 404 });
        }
        
        // 检查是否有支付或充值记录
        const paymentCount = await db.paymentStudent.count({
          where: { studentId },
        });
        
        const topupCount = await db.topupRecord.count({
          where: { studentId },
        });
        
        if (paymentCount > 0 || topupCount > 0) {
          return json(toJsonSafe({
            error: "该学员有支付或充值记录，无法删除",
            paymentCount,
            topupCount,
          }), { status: 400 });
        }
        
        // 删除学员
        await db.student.delete({
          where: { id: studentId },
        });
        
        return json(toJsonSafe({ success: true, message: "学员删除成功" }));
      }

      case "addPayment": {
        const studentId = formData.get("studentId") as string;
        const amount = parseFloat(formData.get("amount") as string);
        const type = formData.get("type") as string;
        const description = formData.get("description") as string;
        
        if (isNaN(amount) || amount <= 0) {
          return json({ error: "金额必须大于0" }, { status: 400 });
        }
        
        if (!['GOODS', 'TUITION', 'OTHER'].includes(type)) {
          return json({ error: "无效的付款类型" }, { status: 400 });
        }
        
        try {
          // 创建付款记录
          const paymentId = crypto.randomUUID();
          await db.$executeRaw`
            INSERT INTO "PaymentStudent" ("id", "studentId", "amount", "type", "description", "paidAt", "createdAt", "updatedAt")
            VALUES (${paymentId}, ${studentId}, ${amount}::numeric, ${type}::text::"PaymentType", ${description || null}, NOW(), NOW(), NOW())
          `;
          
          return json({ success: true, message: "付款记录添加成功" });
        } catch (error) {
          console.error("添加付款记录失败:", error);
          return json({ error: "添加付款记录失败，请重试" }, { status: 500 });
        }
      }

      case "topup": {
        const studentId = formData.get("studentId") as string;
        const amount = parseFloat(formData.get("amount") as string);
        const description = formData.get("description") as string;
        
        if (isNaN(amount) || amount <= 0) {
          return json({ error: "充值金额必须大于0" }, { status: 400 });
        }
        
        try {
          // 获取学员当前余额
          const studentResult = await db.$queryRaw`
            SELECT "balance" FROM "Student" WHERE "id" = ${studentId}
          `;
          
          if (!studentResult || (studentResult as any[]).length === 0) {
            return json({ error: "学员不存在" }, { status: 404 });
          }
          
          const currentBalance = parseFloat((studentResult as any[])[0]?.balance === null ? '0' : (studentResult as any[])[0]?.balance);
          // 计算新余额
          const newBalance = currentBalance + amount;
          
          // 更新学员余额
          await db.$executeRaw`
            UPDATE "Student" SET "balance" = ${newBalance}::numeric, "updatedAt" = NOW() WHERE "id" = ${studentId}
          `;
          
          // 创建充值记录
          const topupId = crypto.randomUUID();
          await db.$executeRaw`
            INSERT INTO "TopupRecord" ("id", "studentId", "amount", "description", "topupAt", "createdAt", "updatedAt")
            VALUES (${topupId}, ${studentId}, ${amount}::numeric, ${description || null}, NOW(), NOW(), NOW())
          `;
          
          return json({ 
            success: true, 
            message: `充值成功，当前余额: ¥${newBalance.toFixed(2)}` 
          });
        } catch (error) {
          console.error("充值失败:", error);
          return json({ error: "充值失败，请重试" }, { status: 500 });
        }
      }

      case "getPaymentRecords": {
        const studentId = formData.get("studentId") as string;
        
        try {
          // 获取付款记录
          const paymentRecords = await db.paymentStudent.findMany({
            where: { studentId },
            orderBy: { paidAt: 'desc' }
          });
          
          return json(toJsonSafe({ success: true, records: paymentRecords }));
        } catch (error) {
          console.error("获取付款记录失败:", error);
          return json(toJsonSafe({ error: "获取付款记录失败" }), { status: 500 });
        }
      }

      case "getTopupRecords": {
        const studentId = formData.get("studentId") as string;
        
        try {
          // 获取充值记录
          const topupRecords = await db.topupRecord.findMany({
            where: { studentId },
            orderBy: { topupAt: 'desc' }
          });
          
          return json(toJsonSafe({ success: true, records: topupRecords }));
        } catch (error) {
          console.error("获取充值记录失败:", error);
          return json(toJsonSafe({ error: "获取充值记录失败" }), { status: 500 });
        }
      }

      case "exportStudents": {
        try {
          // 获取所有学员数据
          const studentsData = await db.$queryRaw`
            SELECT s.*, 
              (SELECT COUNT(*) FROM "PaymentStudent" ps WHERE ps."studentId" = s.id) as payment_count,
              (SELECT COUNT(*) FROM "TopupRecord" tr WHERE tr."studentId" = s.id) as topup_count
            FROM "Student" s
            ORDER BY s."createdAt" DESC
          `;
          
          // 转换数据
          const students = (studentsData as any[]).map(s => ({
            id: s.id,
            name: s.name,
            wechat: s.wechat,
            phone: s.phone || '',
            email: s.email || '',
            storeCount: Number(s.storeCount || 0),
            balance: Number(s.balance || 0),
            remark: s.remark || '',
            paymentCount: Number(s.payment_count || 0),
            topupCount: Number(s.topup_count || 0),
            createdAt: s.createdAt instanceof Date 
              ? new Date(s.createdAt.getTime() - 8 * 60 * 60 * 1000).toLocaleString('zh-CN')
              : new Date(new Date(s.createdAt).getTime() - 8 * 60 * 60 * 1000).toLocaleString('zh-CN')
          }));
          
          // 创建CSV数据
          const headers = ['姓名', '微信号', '电话', '邮箱', '店铺数量', '余额', '备注', '付款记录数量', '充值记录数量', '创建时间'];
          const rows = students.map(s => [
            s.name,
            s.wechat,
            s.phone,
            s.email,
            s.storeCount,
            s.balance.toFixed(2),
            s.remark,
            s.paymentCount,
            s.topupCount,
            s.createdAt
          ]);
          
          // 拼接CSV内容
          const csvContent = [
            headers.join(','),
            ...rows.map(row => row.map(cell => {
              // 处理包含逗号、换行或引号的值
              if (typeof cell === 'string' && (cell.includes(',') || cell.includes('\n') || cell.includes('"'))) {
                return `"${cell.replace(/"/g, '""')}"`;
              }
              return cell;
            }).join(','))
          ].join('\n');
          
          // 添加BOM头，解决Excel中文乱码问题
          const bomPrefix = '\uFEFF';
          const csvWithBOM = bomPrefix + csvContent;
          
          // 设置响应头，创建下载
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          return new Response(csvWithBOM, {
            status: 200,
            headers: {
              'Content-Type': 'text/csv;charset=utf-8',
              'Content-Disposition': `attachment;filename=students-${timestamp}.csv`
            }
          });
        } catch (error) {
          console.error("导出学员数据失败:", error);
          return json(toJsonSafe({ error: "导出学员数据失败，请重试" }), { status: 500 });
        }
      }

      default:
        return json(toJsonSafe({ error: "无效的操作" }), { status: 400 });
    }
  } catch (error) {
    console.error("Action error:", error);
    return json(toJsonSafe({ 
      error: "操作失败，请重试" + (error instanceof Error ? `: ${error.message}` : "") 
    }), { status: 500 });
  }
} 

// 定义列配置
const defaultColumns = [
  { id: 'name', title: '姓名', defaultVisible: true },
  { id: 'wechat', title: '微信号', defaultVisible: true },
  { id: 'phone', title: '电话', defaultVisible: true },
  { id: 'email', title: '邮箱', defaultVisible: true },
  { id: 'storeCount', title: '店铺数量', defaultVisible: true },
  { id: 'balance', title: '余额', defaultVisible: true },
  { id: 'paymentCount', title: '付款记录', defaultVisible: true },
  { id: 'topupCount', title: '充值记录', defaultVisible: true },
  { id: 'createdAt', title: '创建时间', defaultVisible: true },
  { id: 'actions', title: '操作', defaultVisible: true }
];

// 转换付款类型为中文
function getPaymentTypeText(type: string) {
  switch (type) {
    case 'GOODS': return '货款';
    case 'TUITION': return '学费';
    case 'OTHER': return '其他';
    default: return type;
  }
}

export default function StudentManagement() {
  const { students, currentUser, pagination, stats } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const [searchParams] = useSearchParams();
  const actionData = useActionData<typeof action>();
  const isSubmitting = navigation.state === "submitting";
  
  // 确保students是一个数组，即使为空
  const safeStudents = Array.isArray(students) ? students : [];
  
  // 状态变量
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingStudent, setEditingStudent] = useState<any | null>(null);
  const [deletingStudentId, setDeletingStudentId] = useState<string | null>(null);
  const [payingStudent, setPayingStudent] = useState<any | null>(null);
  const [topupStudent, setTopupStudent] = useState<any | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState<'name' | 'phone' | 'email' | 'wechat'>('name');
  const [showError, setShowError] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    defaultColumns.filter(c => c.defaultVisible).map(c => c.id)
  );
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const menuRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

  // 添加查看记录相关的状态变量
  const [viewingPaymentRecords, setViewingPaymentRecords] = useState<any | null>(null);
  const [viewingTopupRecords, setViewingTopupRecords] = useState<any | null>(null);
  const [paymentRecords, setPaymentRecords] = useState<any[]>([]);
  const [topupRecords, setTopupRecords] = useState<any[]>([]);
  const [loadingRecords, setLoadingRecords] = useState(false);

  // 使用 useEffect 处理 localStorage
  useEffect(() => {
    const saved = localStorage.getItem('studentManagementVisibleColumns');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          setVisibleColumns(parsed);
        }
      } catch (e) {
        console.error('Failed to parse visibleColumns from localStorage');
      }
    }
  }, []);

  // 处理列显示切换
  const handleColumnToggle = (columnId: string) => {
    const newVisibleColumns = visibleColumns.includes(columnId)
      ? visibleColumns.filter(id => id !== columnId)
      : [...visibleColumns, columnId];
    setVisibleColumns(newVisibleColumns);
    localStorage.setItem('studentManagementVisibleColumns', JSON.stringify(newVisibleColumns));
  };

  // 关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (activeMenu && menuRefs.current[activeMenu] && 
          !menuRefs.current[activeMenu]?.contains(event.target as Node)) {
        setActiveMenu(null);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMenu]);

  // 处理提交状态和结果
  useEffect(() => {
    if (navigation.state === "idle") {
      setLoadingRecords(false);
      
      if (actionData?.error) {
        setShowToast(true);
        setToastMessage(actionData.error);
        return;
      }
      
      if (actionData?.success) {
        if (actionData.records && viewingPaymentRecords) {
          setPaymentRecords(actionData.records);
        } else if (actionData.records && viewingTopupRecords) {
          setTopupRecords(actionData.records);
        }
        
        if (actionData.message) {
          setToastMessage(actionData.message || "操作成功");
          setShowToast(true);
          
          // 关闭相关对话框
          setIsCreateModalOpen(false);
          setEditingStudent(null);
          setDeletingStudentId(null);
          setPayingStudent(null);
          setTopupStudent(null);
        }
      }
    }
  }, [navigation.state, actionData, viewingPaymentRecords, viewingTopupRecords]);

  // 处理新学员表单提交
  const handleCreateStudent = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "create");
    submit(formData, { method: "post" });
  };

  // 处理编辑学员表单提交
  const handleUpdateStudent = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "update");
    submit(formData, { method: "post" });
  };

  // 处理删除学员
  const handleDeleteStudent = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "delete");
    submit(formData, { method: "post" });
  };

  // 处理添加付款记录
  const handleAddPayment = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "addPayment");
    submit(formData, { method: "post" });
  };
  
  // 处理充值
  const handleTopup = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "topup");
    submit(formData, { method: "post" });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    submit({ 
      page: page.toString(),
      search: searchTerm,
      searchType
    }, { method: "get" });
  };

  // 在 Pagination 组件使用处添加计算逻辑
  const paginationStart = pagination.total > 0 
    ? (pagination.page - 1) * pagination.pageSize + 1 
    : 0;
  const paginationEnd = Math.min(pagination.page * pagination.pageSize, pagination.total);

  // 处理查看付款记录
  const handleViewPaymentRecords = async (student: any) => {
    setViewingPaymentRecords(student);
    setLoadingRecords(true);
    
    try {
      const formData = new FormData();
      formData.append("intent", "getPaymentRecords");
      formData.append("studentId", student.id);
      
      submit(formData, { method: "post" });
    } catch (error) {
      console.error("获取付款记录错误:", error);
      setToastMessage("获取付款记录失败");
      setShowToast(true);
      setLoadingRecords(false);
    }
  };
  
  // 处理查看充值记录
  const handleViewTopupRecords = async (student: any) => {
    setViewingTopupRecords(student);
    setLoadingRecords(true);
    
    try {
      const formData = new FormData();
      formData.append("intent", "getTopupRecords");
      formData.append("studentId", student.id);
      
      submit(formData, { method: "post" });
    } catch (error) {
      console.error("获取充值记录错误:", error);
      setToastMessage("获取充值记录失败");
      setShowToast(true);
      setLoadingRecords(false);
    }
  };

  // 导出学员数据
  const handleExportStudents = () => {
    const formData = new FormData();
    formData.append("intent", "exportStudents");
    submit(formData, { method: "post" });
  };

  // 导出链接（使用直接链接替代表单）
  const exportLink = "/api/export-students";

  return (
    <>
      <AdminLayout user={currentUser}>
        <div className="min-h-screen bg-gray-50 py-4 sm:py-10">
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <div className="max-w-[2000px] mx-auto px-2 sm:px-6 lg:px-8">
              {/* 页面标题和统计信息 */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 gap-4">
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-gray-900">学员管理</h1>
                  <p className="mt-1 text-sm text-gray-500">
                    管理学员信息、付款记录和余额充值
                  </p>
                </div>
                
                {/* 数据统计卡片 */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
                    <div className="text-sm text-gray-500">总人数</div>
                    <div className="font-bold text-lg">{pagination.total}</div>
                  </div>
                  <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
                    <div className="text-sm text-gray-500">总货款</div>
                    <div className="font-bold text-lg">¥{stats.goodsTotal.toFixed(2)}</div>
                  </div>
                  <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
                    <div className="text-sm text-gray-500">总学费</div>
                    <div className="font-bold text-lg">¥{stats.tuitionTotal.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              {/* 搜索和创建按钮 */}
              <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex flex-col items-start w-full sm:w-auto sm:flex-row sm:items-center sm:space-x-3 sm:flex-1 sm:max-w-xl">
                  <div className="flex items-center justify-between w-full sm:w-auto mb-2 sm:mb-0 bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
                    <span className="text-sm font-medium text-gray-600 mr-2">搜索类型：</span>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          value="name"
                          checked={searchType === 'name'}
                          onChange={(e) => setSearchType(e.target.value as any)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">姓名</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          value="phone"
                          checked={searchType === 'phone'}
                          onChange={(e) => setSearchType(e.target.value as any)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">电话</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          value="email"
                          checked={searchType === 'email'}
                          onChange={(e) => setSearchType(e.target.value as any)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">邮箱</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          value="wechat"
                          checked={searchType === 'wechat'}
                          onChange={(e) => setSearchType(e.target.value as any)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">微信号</span>
                      </label>
                    </div>
                  </div>
                  <div className="relative flex-1 w-full mt-2 sm:mt-0">
                    <div className="flex shadow-sm">
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            submit(
                              { search: searchTerm, searchType, page: '1' },
                              { method: "get" }
                            );
                          }
                        }}
                        placeholder={`搜索${searchType === 'name' ? '姓名' : searchType === 'phone' ? '电话' : searchType === 'email' ? '邮箱' : '微信号'}`}
                        className="h-11 w-full rounded-l-lg border-gray-200 pl-3 pr-10 text-sm focus:border-indigo-500 focus:ring-indigo-500"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          submit(
                            { search: searchTerm, searchType, page: '1' },
                            { method: "get" }
                          );
                        }}
                        className="bg-indigo-600 text-white px-4 rounded-r-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center justify-center"
                      >
                        <MagnifyingGlassIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap items-center justify-between w-full sm:w-auto gap-3 sm:gap-6 mt-4 sm:mt-0">
                  <button
                    type="button"
                    onClick={() => {
                      console.log("Opening modal...");
                      setIsCreateModalOpen(true);
                    }}
                    className="inline-flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ml-auto"
                  >
                    <PlusIcon className="mr-2 h-5 w-5" />
                    <span className="hidden xs:inline">添加学员</span>
                    <span className="xs:hidden">添加</span>
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
                <div className="mb-2 sm:mb-0">
                  <ColumnSelector
                    columns={defaultColumns}
                    visibleColumns={visibleColumns}
                    onColumnToggle={handleColumnToggle}
                  />
                </div>
                
                {/* 导出Excel按钮 */}
                <a
                  href={exportLink}
                  className="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ArrowDownTrayIcon className="mr-2 h-5 w-5" />
                  导出Excel
                </a>
              </div>

              {/* 表格容器 */}
              <div className="bg-white/30 backdrop-blur-md rounded-xl shadow-sm border border-gray-200/50">
                <div className="border-b border-gray-200">
                  <div className="px-4 sm:px-6 py-4 border-b border-gray-200/50">
                    <div className="flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900">
                        学员列表
                      </h2>
                      <span className="text-sm text-gray-500">
                        共 {pagination.total} 条记录
                      </span>
                    </div>
                  </div>

                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="relative">
                      <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300">
                        <table className="w-full">
                          <colgroup>
                            {visibleColumns.includes('name') && <col className="w-36" />}
                            {visibleColumns.includes('wechat') && <col className="w-36" />}
                            {visibleColumns.includes('phone') && <col className="w-36" />}
                            {visibleColumns.includes('email') && <col className="w-52" />}
                            {visibleColumns.includes('storeCount') && <col className="w-24" />}
                            {visibleColumns.includes('balance') && <col className="w-24" />}
                            {visibleColumns.includes('paymentCount') && <col className="w-24" />}
                            {visibleColumns.includes('topupCount') && <col className="w-24" />}
                            {visibleColumns.includes('createdAt') && <col className="w-40" />}
                            {visibleColumns.includes('actions') && <col className="w-48" />}
                          </colgroup>
                          <thead className="bg-gray-50">
                            <tr>
                              {defaultColumns.map(column => 
                                visibleColumns.includes(column.id) && (
                                  <th
                                    key={column.id}
                                    scope="col"
                                    className={`
                                      px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap
                                      ${column.id === 'actions' ? 'sticky right-0 bg-gray-50 shadow-l z-10' : ''}
                                    `}
                                  >
                                    {column.title}
                                  </th>
                                )
                              )}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {safeStudents.map((student) => (
                              <tr key={student?.id || 'temp-key'} className="hover:bg-gray-50">
                                {visibleColumns.includes('name') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    <div className="font-medium text-gray-900">
                                      {student?.name || '未命名'}
                                    </div>
                                  </td>
                                )}
                                {visibleColumns.includes('wechat') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    {student?.wechat || "-"}
                                  </td>
                                )}
                                {visibleColumns.includes('phone') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    {student?.phone || "-"}
                                  </td>
                                )}
                                {visibleColumns.includes('email') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    <span className="max-w-[120px] truncate block sm:max-w-none">
                                      {student?.email || "-"}
                                    </span>
                                  </td>
                                )}
                                {visibleColumns.includes('storeCount') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-center">
                                    {student?.storeCount || 0}
                                  </td>
                                )}
                                {visibleColumns.includes('balance') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    <span className={`font-medium ${Number(student?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-500'}`}>
                                      ¥{Number(student?.balance || 0).toFixed(2)}
                                    </span>
                                  </td>
                                )}
                                {visibleColumns.includes('paymentCount') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-center">
                                    <button 
                                      onClick={() => handleViewPaymentRecords(student)}
                                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                                    >
                                      {student?._count?.payments || 0}
                                    </button>
                                  </td>
                                )}
                                {visibleColumns.includes('topupCount') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-center">
                                    <button
                                      onClick={() => handleViewTopupRecords(student)}
                                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer"
                                    >
                                      {student?._count?.topups || 0}
                                    </button>
                                  </td>
                                )}
                                {visibleColumns.includes('createdAt') && (
                                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                    {student?.createdAt ? (() => {
                                      // 创建日期对象并调整时区
                                      const date = new Date(student.createdAt);
                                      // 检查日期是否有效
                                      if (isNaN(date.getTime())) {
                                        return '-';
                                      }
                                      
                                      // 调整时区，减8小时
                                      const localDate = new Date(date.getTime() - 8 * 60 * 60 * 1000);
                                      
                                      return localDate.toLocaleString('zh-CN', {
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      });
                                    })() : '-'}
                                  </td>
                                )}
                                {visibleColumns.includes('actions') && (
                                  <td className="sticky right-0 bg-white shadow-l z-10 px-2 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div className="flex flex-wrap justify-end gap-2">
                                      <div className="hidden sm:flex gap-2">
                                        <button
                                          type="button"
                                          onClick={() => setEditingStudent(student)}
                                          className="text-indigo-600 hover:text-indigo-900"
                                        >
                                          编辑
                                        </button>
                                        <button
                                          type="button"
                                          onClick={() => setPayingStudent(student)}
                                          className="text-amber-600 hover:text-amber-900"
                                        >
                                          付款
                                        </button>
                                        <button
                                          type="button"
                                          onClick={() => setTopupStudent(student)}
                                          className="text-green-600 hover:text-green-900"
                                        >
                                          充值
                                        </button>
                                        <button
                                          type="button"
                                          onClick={() => setDeletingStudentId(student?.id || '')}
                                          className="text-red-600 hover:text-red-900"
                                          disabled={student?._count?.payments > 0 || student?._count?.topups > 0}
                                          title={student?._count?.payments > 0 || student?._count?.topups > 0 ? "有相关记录，无法删除" : ""}
                                        >
                                          删除
                                        </button>
                                      </div>
                                      <div className="flex sm:hidden">
                                        <div className="relative" ref={el => menuRefs.current[student?.id || 'temp-key'] = el}>
                                          <button
                                            type="button"
                                            onClick={() => setActiveMenu(activeMenu === student?.id ? null : student?.id)}
                                            className="rounded-full bg-gray-100 p-1 text-gray-500 hover:text-gray-700"
                                          >
                                            <EllipsisHorizontalIcon className="h-5 w-5" />
                                          </button>

                                          {activeMenu === student?.id && (
                                            <div className="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-20">
                                              <button
                                                type="button"
                                                onClick={() => {
                                                  setEditingStudent(student);
                                                  setActiveMenu(null);
                                                }}
                                                className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                              >
                                                编辑
                                              </button>
                                              <button
                                                type="button"
                                                onClick={() => {
                                                  setPayingStudent(student);
                                                  setActiveMenu(null);
                                                }}
                                                className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                              >
                                                付款
                                              </button>
                                              <button
                                                type="button"
                                                onClick={() => {
                                                  setTopupStudent(student);
                                                  setActiveMenu(null);
                                                }}
                                                className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                              >
                                                充值
                                              </button>
                                              <button
                                                type="button"
                                                onClick={() => {
                                                  setDeletingStudentId(student?.id || '');
                                                  setActiveMenu(null);
                                                }}
                                                className={`block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 ${
                                                  student?._count?.payments > 0 || student?._count?.topups > 0 ? "opacity-50 cursor-not-allowed" : ""
                                                }`}
                                                disabled={student?._count?.payments > 0 || student?._count?.topups > 0}
                                              >
                                                删除
                                              </button>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                )}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    显示第 <span className="font-medium">{paginationStart}</span> 到{" "}
                    <span className="font-medium">{paginationEnd}</span> 条，共{" "}
                    <span className="font-medium">{pagination.total}</span> 条记录
                  </p>
                </div>
                <div>
                  <Pagination
                    currentPage={pagination.page || 1}
                    totalPages={pagination.totalPages || 1}
                    total={pagination.total || 0}
                    pageSize={pagination.pageSize || 10}
                    onPageChange={handlePageChange}
                  />
                </div>
              </div>
              <div className="flex sm:hidden flex-1 justify-between">
                <button
                  onClick={() => {
                    if (pagination.page > 1) {
                      handlePageChange(pagination.page - 1);
                    }
                  }}
                  disabled={pagination.page <= 1}
                  className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                    pagination.page <= 1 
                      ? "text-gray-300 cursor-not-allowed"
                      : "text-gray-700 bg-white hover:bg-gray-50"
                  }`}
                >
                  上一页
                </button>
                <span className="text-sm text-gray-700">
                  第 {pagination.page} / {pagination.totalPages} 页
                </span>
                <button
                  onClick={() => {
                    if (pagination.page < pagination.totalPages) {
                      handlePageChange(pagination.page + 1);
                    }
                  }}
                  disabled={pagination.page >= pagination.totalPages}
                  className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                    pagination.page >= pagination.totalPages 
                      ? "text-gray-300 cursor-not-allowed"
                      : "text-gray-700 bg-white hover:bg-gray-50"
                  }`}
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>

      {/* 创建学员模态框 */}
      <Modal
        isOpen={isCreateModalOpen}
        title="添加学员"
        onClose={() => setIsCreateModalOpen(false)}
        maxWidth="sm:max-w-md"
      >
        <Form method="post" onSubmit={handleCreateStudent} className="space-y-4">
          <div className="text-sm text-gray-500 mb-2">
            带 <span className="text-red-500">*</span> 的字段为必填项
          </div>
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              姓名 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="name"
                id="name"
                required
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="wechat" className="block text-sm font-medium text-gray-700">
              微信号 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="wechat"
                id="wechat"
                required
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              电话
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="phone"
                id="phone"
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              邮箱
            </label>
            <div className="mt-1">
              <input
                type="email"
                name="email"
                id="email"
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="balance" className="block text-sm font-medium text-gray-700">
              初始余额
            </label>
            <div className="mt-1">
              <input
                type="number"
                step="0.01"
                name="balance"
                id="balance"
                defaultValue="0"
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="storeCount" className="block text-sm font-medium text-gray-700">
              店铺数量 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="storeCount"
                id="storeCount"
                required
                defaultValue="0"
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <input type="hidden" name="intent" value="create" />

          <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              type="submit"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm"
            >
              创建
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
              onClick={() => setIsCreateModalOpen(false)}
            >
              取消
            </button>
          </div>
        </Form>
      </Modal>

      {/* 编辑学员模态框 */}
      <Modal
        isOpen={!!editingStudent}
        title="编辑学员"
        onClose={() => setEditingStudent(null)}
        maxWidth="sm:max-w-md"
      >
        <Form method="post" onSubmit={handleUpdateStudent} className="space-y-4">
          <div className="text-sm text-gray-500 mb-2">
            带 <span className="text-red-500">*</span> 的字段为必填项
          </div>
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              姓名 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="name"
                id="name"
                required
                defaultValue={editingStudent?.name || ''}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="wechat" className="block text-sm font-medium text-gray-700">
              微信号 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="wechat"
                id="wechat"
                required
                defaultValue={editingStudent?.wechat || ''}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              电话
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="phone"
                id="phone"
                defaultValue={editingStudent?.phone || ''}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              邮箱
            </label>
            <div className="mt-1">
              <input
                type="email"
                name="email"
                id="email"
                defaultValue={editingStudent?.email || ''}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label htmlFor="storeCount" className="block text-sm font-medium text-gray-700">
              店铺数量 <span className="text-red-500">*</span>
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="storeCount"
                id="storeCount"
                required
                defaultValue={editingStudent?.storeCount || 0}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <input type="hidden" name="id" value={editingStudent?.id || ''} />
          <input type="hidden" name="intent" value="update" />

          <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              type="submit"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm"
            >
              保存
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
              onClick={() => setEditingStudent(null)}
            >
              取消
            </button>
          </div>
        </Form>
      </Modal>

      {/* 付款模态框 */}
      <Modal
        isOpen={!!payingStudent}
        title={payingStudent ? `${payingStudent.name || '未命名学员'} - 付款记录` : ""}
        onClose={() => setPayingStudent(null)}
        maxWidth="sm:max-w-lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">当前余额</div>
                <div className={`font-bold text-xl ${Number(payingStudent?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-700'}`}>
                  ¥{Number(payingStudent?.balance || 0).toFixed(2)}
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">微信号</div>
                <div className="font-medium text-gray-700">{payingStudent?.wechat || '-'}</div>
              </div>
            </div>
          </div>

          <Form method="post" onSubmit={handleAddPayment} className="space-y-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                付款金额
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  step="0.01"
                  name="amount"
                  id="amount"
                  required
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                付款类型
              </label>
              <select
                id="type"
                name="type"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                <option value="GOODS">货款</option>
                <option value="TUITION">学费</option>
              </select>
            </div>

            <div>
              <label htmlFor="note" className="block text-sm font-medium text-gray-700">
                备注信息
              </label>
              <div className="mt-1">
                <textarea
                  id="note"
                  name="note"
                  rows={3}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <input type="hidden" name="studentId" value={payingStudent?.id || ''} />
            <input type="hidden" name="intent" value="addPayment" />

            <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-amber-600 text-base font-medium text-white hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:col-start-2 sm:text-sm"
              >
                <CreditCardIcon className="mr-2 h-5 w-5" aria-hidden="true" />
                确认付款
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
                onClick={() => setPayingStudent(null)}
              >
                取消
              </button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* 充值模态框 */}
      <Modal
        isOpen={!!topupStudent} 
        title={topupStudent ? `${topupStudent.name || '未命名学员'} - 余额充值` : ""}
        onClose={() => setTopupStudent(null)}
        maxWidth="sm:max-w-md"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">当前余额</div>
                <div className={`font-bold text-xl ${Number(topupStudent?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-700'}`}>
                  ¥{Number(topupStudent?.balance || 0).toFixed(2)}
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">微信号</div>
                <div className="font-medium text-gray-700">{topupStudent?.wechat || '-'}</div>
              </div>
            </div>
          </div>

          <Form method="post" onSubmit={handleTopup} className="space-y-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                充值金额
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  step="0.01"
                  name="amount"
                  id="amount"
                  required
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div>
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700">
                支付方式
              </label>
              <select
                id="paymentMethod"
                name="paymentMethod"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                <option value="CASH">现金</option>
                <option value="WECHAT">微信</option>
                <option value="ALIPAY">支付宝</option>
                <option value="BANK">银行转账</option>
              </select>
            </div>

            <div>
              <label htmlFor="note" className="block text-sm font-medium text-gray-700">
                备注信息
              </label>
              <div className="mt-1">
                <textarea
                  id="note"
                  name="note"
                  rows={3}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <input type="hidden" name="studentId" value={topupStudent?.id || ''} />
            <input type="hidden" name="intent" value="topup" />

            <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:col-start-2 sm:text-sm"
              >
                <CurrencyYenIcon className="mr-2 h-5 w-5" aria-hidden="true" />
                确认充值
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
                onClick={() => setTopupStudent(null)}
              >
                取消
              </button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* 删除确认对话框 */}
      {deletingStudentId && (
        <ConfirmDialog
          open={!!deletingStudentId}
          title="删除学员"
          message="确定要删除这名学员吗？此操作无法撤销。"
          confirmText="删除"
          onConfirm={() => {
            const formData = new FormData();
            formData.append("studentId", deletingStudentId);
            formData.append("intent", "delete");
            submit(formData, { method: "post" });
            setDeletingStudentId(null);
          }}
          onCancel={() => setDeletingStudentId(null)}
        />
      )}

      {/* 操作成功提示 */}
      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
        />
      )}

      {/* 操作失败提示 */}
      {actionData?.error && (
        <Toast
          message={actionData.error}
          type="error"
          onClose={() => {}}
        />
      )}

      {/* 付款记录模态框 */}
      <Modal
        isOpen={!!viewingPaymentRecords}
        title={viewingPaymentRecords ? `${viewingPaymentRecords.name || '未命名学员'} - 付款记录` : ""}
        onClose={() => setViewingPaymentRecords(null)}
        maxWidth="sm:max-w-4xl"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">学员姓名</div>
                <div className="font-medium text-gray-700">{viewingPaymentRecords?.name || '-'}</div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">微信号</div>
                <div className="font-medium text-gray-700">{viewingPaymentRecords?.wechat || '-'}</div>
              </div>
            </div>
          </div>

          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">付款记录明细</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">共 {paymentRecords.length} 条记录</p>
            </div>
            
            {loadingRecords ? (
              <div className="flex justify-center items-center py-12">
                <svg className="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : paymentRecords.length === 0 ? (
              <div className="flex justify-center items-center py-12 text-gray-500">暂无付款记录</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        付款时间
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        金额
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        类型
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        备注
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paymentRecords.map((record: any) => (
                      <tr key={record.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.paidAt ? (() => {
                            const date = new Date(record.paidAt);
                            if (isNaN(date.getTime())) return '-';
                            const localDate = new Date(date.getTime() - 8 * 60 * 60 * 1000);
                            return localDate.toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            });
                          })() : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          ¥{Number(record.amount).toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {record.type === 'GOODS' ? '货款' : 
                           record.type === 'TUITION' ? '学费' : 
                           record.type === 'OTHER' ? '其他' : record.type}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {record.description || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={() => setViewingPaymentRecords(null)}
              className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm"
            >
              关闭
            </button>
          </div>
        </div>
      </Modal>

      {/* 充值记录模态框 */}
      <Modal
        isOpen={!!viewingTopupRecords}
        title={viewingTopupRecords ? `${viewingTopupRecords.name || '未命名学员'} - 充值记录` : ""}
        onClose={() => setViewingTopupRecords(null)}
        maxWidth="sm:max-w-4xl"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">学员姓名</div>
                <div className="font-medium text-gray-700">{viewingTopupRecords?.name || '-'}</div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">当前余额</div>
                <div className={`font-medium ${Number(viewingTopupRecords?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-700'}`}>
                  ¥{Number(viewingTopupRecords?.balance || 0).toFixed(2)}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">充值记录明细</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">共 {topupRecords.length} 条记录</p>
            </div>
            
            {loadingRecords ? (
              <div className="flex justify-center items-center py-12">
                <svg className="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : topupRecords.length === 0 ? (
              <div className="flex justify-center items-center py-12 text-gray-500">暂无充值记录</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        充值时间
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        金额
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        备注
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {topupRecords.map((record: any) => (
                      <tr key={record.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.topupAt ? (() => {
                            const date = new Date(record.topupAt);
                            if (isNaN(date.getTime())) return '-';
                            const localDate = new Date(date.getTime() - 8 * 60 * 60 * 1000);
                            return localDate.toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            });
                          })() : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                          ¥{Number(record.amount).toFixed(2)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {record.description || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={() => setViewingTopupRecords(null)}
              className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm"
            >
              关闭
            </button>
          </div>
        </div>
      </Modal>
    </>
  );
} 